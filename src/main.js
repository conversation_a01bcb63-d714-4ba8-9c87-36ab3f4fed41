import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import router from "./router";
import App from "./App.vue";
import "@/assets/styles/common.scss";

import process from "process/browser";
const app = createApp(App);
// app.provide("process", process);
app.config.globalProperties.process = process;
app.use(ElementPlus);
app.use(createPinia());
app.use(router);
app.mount("#app");
