# CircuitBoard 电路板动画组件

一个基于 Vue3 + SVG 的电路板动画效果组件，提供炫酷的科技感视觉效果。

## 功能特性

- ⚡ **SVG矢量图形**：支持任意缩放，保持清晰度
- 🔥 **多种动画效果**：电路发光、芯片脉冲、数据流动
- 🎯 **丰富的电路元件**：CPU、RAM、SSD、电阻、电容、连接点
- 🌟 **可控制动画**：支持启动/停止动画
- 📱 **响应式设计**：适配不同屏幕尺寸
- 🎨 **科技感配色**：绿色电路线、黄色连接点、蓝色电容

## 组件结构

### 主要元素

1. **电路板基底**：深绿色背景，模拟真实PCB板
2. **电路线路**：发光的绿色线路，带有呼吸动画
3. **芯片组件**：
   - 主处理器 (CPU)：大型芯片，带有强烈脉冲效果
   - 内存 (RAM)：中等尺寸芯片
   - 存储 (SSD)：中等尺寸芯片
4. **电子元件**：
   - 电阻：棕色矩形，带有热效应动画
   - 电容：蓝色圆形，带有充电动画
5. **连接点**：黄色圆点，闪烁效果
6. **数据流**：彩色光点沿电路移动

## 使用方法

### 基础用法

```vue
<template>
  <CircuitBoard />
</template>

<script setup>
import CircuitBoard from '@/components/CircuitBoard.vue'
</script>
```

### 自定义尺寸

```vue
<template>
  <CircuitBoard 
    :width="1000" 
    :height="750" 
  />
</template>
```

### 控制动画

```vue
<template>
  <div>
    <CircuitBoard 
      ref="circuitRef"
      :animated="isAnimated"
    />
    <button @click="toggleAnimation">切换动画</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CircuitBoard from '@/components/CircuitBoard.vue'

const circuitRef = ref(null)
const isAnimated = ref(true)

const toggleAnimation = () => {
  if (isAnimated.value) {
    circuitRef.value.stopAnimation()
  } else {
    circuitRef.value.startAnimation()
  }
  isAnimated.value = !isAnimated.value
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| width | Number | 800 | 组件宽度（像素） |
| height | Number | 600 | 组件高度（像素） |
| animated | Boolean | true | 是否启用动画 |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| startAnimation | 启动动画 | - |
| stopAnimation | 停止动画 | - |

## 样式定制

组件使用 SCSS 编写样式，支持以下自定义：

### CSS 变量

```scss
.circuit-board-container {
  --circuit-color: #00ff41;      // 电路颜色
  --connection-color: #ffff00;   // 连接点颜色
  --chip-color: #333;            // 芯片颜色
  --background-color: #0a1a0a;   // 背景颜色
}
```

### 动画时长

```scss
.circuit-line {
  animation-duration: 2s; // 电路发光动画时长
}

.chip {
  animation-duration: 3s; // 芯片脉冲动画时长
}
```

## 动画效果说明

1. **电路发光**：电路线路呼吸式发光效果
2. **芯片脉冲**：芯片颜色和发光强度周期性变化
3. **连接点闪烁**：连接点透明度周期性变化
4. **电阻发热**：电阻颜色模拟发热效果
5. **电容充电**：电容大小和颜色周期性变化
6. **数据流动**：彩色光点沿电路路径移动

## 技术实现

- **SVG绘图**：使用SVG元素绘制电路板和元件
- **CSS动画**：使用@keyframes定义各种动画效果
- **Vue3组合式API**：使用setup语法糖和响应式数据
- **SCSS预处理器**：使用嵌套语法和变量

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 组件使用SVG，在低端设备上可能影响性能
2. 动画效果较多，建议在必要时关闭动画
3. 组件尺寸建议不要过大，以免影响页面性能
4. 支持响应式，但建议在容器中使用固定尺寸

## 示例项目

查看完整示例：`/circuit-board` 路由页面
