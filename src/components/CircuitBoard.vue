<template>
  <div class="circuit-board-container" :style="{ width: width + 'px', height: height + 'px' }">
    <svg 
      :width="width" 
      :height="height" 
      class="circuit-board-svg"
      viewBox="0 0 800 600"
    >
      <!-- 背景 -->
      <rect width="100%" height="100%" fill="#0a1a0a" />
      
      <!-- 电路板基底 -->
      <rect 
        x="50" 
        y="50" 
        width="700" 
        height="500" 
        fill="#1a3d1a" 
        stroke="#2d5a2d" 
        stroke-width="2"
        rx="10"
      />
      
      <!-- 主要电路线路 -->
      <g class="circuit-lines">
        <!-- 水平线路 -->
        <line x1="100" y1="150" x2="700" y2="150" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="100" y1="250" x2="700" y2="250" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="100" y1="350" x2="700" y2="350" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="100" y1="450" x2="700" y2="450" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        
        <!-- 垂直线路 -->
        <line x1="200" y1="100" x2="200" y2="500" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="350" y1="100" x2="350" y2="500" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="500" y1="100" x2="500" y2="500" stroke="#00ff41" stroke-width="3" class="circuit-line" />
        <line x1="650" y1="100" x2="650" y2="500" stroke="#00ff41" stroke-width="3" class="circuit-line" />
      </g>
      
      <!-- 芯片组件 -->
      <g class="chips">
        <!-- 主处理器 -->
        <rect 
          x="300" 
          y="200" 
          width="100" 
          height="100" 
          fill="#333" 
          stroke="#666" 
          stroke-width="2"
          class="chip main-chip"
        />
        <text x="350" y="255" text-anchor="middle" fill="#00ff41" font-size="12" font-family="monospace">CPU</text>
        
        <!-- 内存芯片 -->
        <rect 
          x="450" 
          y="180" 
          width="80" 
          height="40" 
          fill="#2a2a2a" 
          stroke="#555" 
          stroke-width="1"
          class="chip memory-chip"
        />
        <text x="490" y="205" text-anchor="middle" fill="#00ff41" font-size="10" font-family="monospace">RAM</text>
        
        <!-- 存储芯片 -->
        <rect 
          x="450" 
          y="280" 
          width="80" 
          height="40" 
          fill="#2a2a2a" 
          stroke="#555" 
          stroke-width="1"
          class="chip storage-chip"
        />
        <text x="490" y="305" text-anchor="middle" fill="#00ff41" font-size="10" font-family="monospace">SSD</text>
      </g>
      
      <!-- 电阻和电容 -->
      <g class="components">
        <!-- 电阻 -->
        <rect x="150" y="140" width="30" height="10" fill="#8B4513" stroke="#654321" class="resistor" />
        <rect x="150" y="240" width="30" height="10" fill="#8B4513" stroke="#654321" class="resistor" />
        <rect x="150" y="340" width="30" height="10" fill="#8B4513" stroke="#654321" class="resistor" />
        
        <!-- 电容 -->
        <circle cx="250" cy="200" r="8" fill="#4169E1" stroke="#1E90FF" class="capacitor" />
        <circle cx="250" cy="300" r="8" fill="#4169E1" stroke="#1E90FF" class="capacitor" />
        <circle cx="250" cy="400" r="8" fill="#4169E1" stroke="#1E90FF" class="capacitor" />
      </g>
      
      <!-- 连接点 -->
      <g class="connection-points">
        <circle cx="200" cy="150" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="350" cy="150" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="500" cy="150" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="650" cy="150" r="4" fill="#ffff00" class="connection-point" />
        
        <circle cx="200" cy="250" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="350" cy="250" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="500" cy="250" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="650" cy="250" r="4" fill="#ffff00" class="connection-point" />
        
        <circle cx="200" cy="350" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="350" cy="350" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="500" cy="350" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="650" cy="350" r="4" fill="#ffff00" class="connection-point" />
        
        <circle cx="200" cy="450" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="350" cy="450" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="500" cy="450" r="4" fill="#ffff00" class="connection-point" />
        <circle cx="650" cy="450" r="4" fill="#ffff00" class="connection-point" />
      </g>
      
      <!-- 数据流动效果 -->
      <g class="data-flow">
        <circle r="3" fill="#00ffff" class="data-pulse">
          <animateMotion dur="3s" repeatCount="indefinite">
            <path d="M100,150 L700,150" />
          </animateMotion>
        </circle>
        <circle r="3" fill="#ff00ff" class="data-pulse">
          <animateMotion dur="4s" repeatCount="indefinite">
            <path d="M200,100 L200,500" />
          </animateMotion>
        </circle>
        <circle r="3" fill="#ffff00" class="data-pulse">
          <animateMotion dur="5s" repeatCount="indefinite">
            <path d="M100,250 L700,250" />
          </animateMotion>
        </circle>
      </g>
    </svg>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  },
  animated: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const isActive = ref(false)

// 生命周期
onMounted(() => {
  if (props.animated) {
    isActive.value = true
  }
})

onUnmounted(() => {
  isActive.value = false
})

// 方法
const startAnimation = () => {
  isActive.value = true
}

const stopAnimation = () => {
  isActive.value = false
}

// 暴露方法给父组件
defineExpose({
  startAnimation,
  stopAnimation
})
</script>

<style lang="scss" scoped>
.circuit-board-container {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
}

.circuit-board-svg {
  width: 100%;
  height: 100%;
}

// 电路线路动画
.circuit-line {
  filter: drop-shadow(0 0 3px #00ff41);
  animation: circuit-glow 2s ease-in-out infinite alternate;
}

@keyframes circuit-glow {
  from {
    stroke-opacity: 0.6;
    filter: drop-shadow(0 0 3px #00ff41);
  }
  to {
    stroke-opacity: 1;
    filter: drop-shadow(0 0 8px #00ff41);
  }
}

// 芯片动画
.chip {
  animation: chip-pulse 3s ease-in-out infinite;
}

.main-chip {
  animation: main-chip-pulse 2s ease-in-out infinite;
}

@keyframes chip-pulse {
  0%, 100% {
    fill: #333;
    stroke: #666;
  }
  50% {
    fill: #444;
    stroke: #777;
  }
}

@keyframes main-chip-pulse {
  0%, 100% {
    fill: #333;
    stroke: #666;
    filter: drop-shadow(0 0 5px #00ff41);
  }
  50% {
    fill: #444;
    stroke: #00ff41;
    filter: drop-shadow(0 0 10px #00ff41);
  }
}

// 连接点闪烁
.connection-point {
  animation: connection-blink 1.5s ease-in-out infinite;
}

@keyframes connection-blink {
  0%, 50%, 100% {
    fill-opacity: 1;
    filter: drop-shadow(0 0 3px #ffff00);
  }
  25%, 75% {
    fill-opacity: 0.3;
    filter: drop-shadow(0 0 1px #ffff00);
  }
}

// 电阻动画
.resistor {
  animation: resistor-heat 4s ease-in-out infinite;
}

@keyframes resistor-heat {
  0%, 100% {
    fill: #8B4513;
  }
  50% {
    fill: #CD853F;
  }
}

// 电容动画
.capacitor {
  animation: capacitor-charge 3s ease-in-out infinite;
}

@keyframes capacitor-charge {
  0%, 100% {
    fill: #4169E1;
    r: 8;
  }
  50% {
    fill: #1E90FF;
    r: 10;
  }
}

// 数据脉冲
.data-pulse {
  filter: drop-shadow(0 0 5px currentColor);
  animation: pulse-glow 1s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    fill-opacity: 0.6;
  }
  to {
    fill-opacity: 1;
  }
}
</style>
