<template>
  <div class="complex-form-page">
    <div class="form-header">
      <h1>复杂表单录入系统</h1>
      <p>支持RPA自动录入的多模块表单</p>
    </div>

    <el-card class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="complex-form"
        data-testid="complex-form"
      >
        <!-- 基本信息模块 -->
        <el-divider content-position="left">
          <span class="section-title">基本信息</span>
        </el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input 
                v-model="formData.name" 
                placeholder="请输入姓名"
                data-testid="name-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select 
                v-model="formData.gender" 
                placeholder="请选择性别"
                data-testid="gender-select"
              >
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="formData.birthDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
                data-testid="birth-date-picker"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input 
                v-model="formData.idCard" 
                placeholder="请输入身份证号"
                data-testid="id-card-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 联系方式模块 -->
        <el-divider content-position="left">
          <span class="section-title">联系方式</span>
        </el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input 
                v-model="formData.phone" 
                placeholder="请输入手机号码"
                data-testid="phone-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="email">
              <el-input 
                v-model="formData.email" 
                placeholder="请输入邮箱地址"
                data-testid="email-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="详细地址" prop="address">
          <el-input 
            v-model="formData.address" 
            type="textarea" 
            :rows="3"
            placeholder="请输入详细地址"
            data-testid="address-textarea"
          />
        </el-form-item>

        <!-- 工作信息模块 -->
        <el-divider content-position="left">
          <span class="section-title">工作信息</span>
        </el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司名称" prop="company">
              <el-input 
                v-model="formData.company" 
                placeholder="请输入公司名称"
                data-testid="company-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input 
                v-model="formData.position" 
                placeholder="请输入职位"
                data-testid="position-input"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作年限" prop="experience">
              <el-select 
                v-model="formData.experience" 
                placeholder="请选择工作年限"
                data-testid="experience-select"
              >
                <el-option label="1年以下" value="0-1" />
                <el-option label="1-3年" value="1-3" />
                <el-option label="3-5年" value="3-5" />
                <el-option label="5-10年" value="5-10" />
                <el-option label="10年以上" value="10+" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="月薪范围" prop="salary">
              <el-select 
                v-model="formData.salary" 
                placeholder="请选择月薪范围"
                data-testid="salary-select"
              >
                <el-option label="5K以下" value="0-5k" />
                <el-option label="5K-10K" value="5k-10k" />
                <el-option label="10K-20K" value="10k-20k" />
                <el-option label="20K-30K" value="20k-30k" />
                <el-option label="30K以上" value="30k+" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 技能和兴趣 -->
        <el-divider content-position="left">
          <span class="section-title">技能和兴趣</span>
        </el-divider>

        <el-form-item label="技能标签" prop="skills">
          <el-checkbox-group v-model="formData.skills" data-testid="skills-checkbox">
            <el-checkbox label="JavaScript" value="javascript" />
            <el-checkbox label="Vue.js" value="vue" />
            <el-checkbox label="React" value="react" />
            <el-checkbox label="Node.js" value="nodejs" />
            <el-checkbox label="Python" value="python" />
            <el-checkbox label="Java" value="java" />
            <el-checkbox label="PHP" value="php" />
            <el-checkbox label="Go" value="go" />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="兴趣爱好" prop="hobbies">
          <el-input 
            v-model="formData.hobbies" 
            type="textarea" 
            :rows="3"
            placeholder="请描述您的兴趣爱好"
            data-testid="hobbies-textarea"
          />
        </el-form-item>

        <!-- 文件上传 -->
        <el-divider content-position="left">
          <span class="section-title">文件上传</span>
        </el-divider>

        <el-form-item label="简历文件" prop="resume">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleResumeChange"
            data-testid="resume-upload"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 pdf/doc/docx 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item class="submit-section">
          <el-button 
            type="primary" 
            @click="submitForm" 
            :loading="submitting"
            data-testid="submit-button"
          >
            提交表单
          </el-button>
          <el-button @click="resetForm" data-testid="reset-button">
            重置表单
          </el-button>
          <el-button 
            type="success" 
            @click="fillSampleData" 
            data-testid="sample-data-button"
          >
            填充示例数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- RPA控制面板 -->
    <el-card class="rpa-panel" v-if="showRpaPanel">
      <template #header>
        <div class="card-header">
          <span>RPA自动录入控制面板</span>
          <el-button type="text" @click="showRpaPanel = false">收起</el-button>
        </div>
      </template>
      
      <div class="rpa-controls">
        <el-row :gutter="15">
          <el-col :span="6">
            <el-button
              type="primary"
              @click="startRpaTask"
              :loading="rpaRunning"
              :disabled="!canStart"
              data-testid="start-rpa-button"
              style="width: 100%"
            >
              启动RPA录入
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button
              type="warning"
              @click="stopRpaTask"
              :disabled="!canStop"
              data-testid="stop-rpa-button"
              style="width: 100%"
            >
              停止任务
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button
              type="success"
              @click="startBatchProcess"
              :disabled="rpaRunning"
              data-testid="batch-process-button"
              style="width: 100%"
            >
              批量处理
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button
              type="info"
              @click="showRpaLogs = !showRpaLogs"
              data-testid="toggle-logs-button"
              style="width: 100%"
            >
              {{ showRpaLogs ? '隐藏' : '显示' }}日志
            </el-button>
          </el-col>
        </el-row>

        <!-- 进度条 -->
        <div v-if="rpaRunning && rpaProgress.total > 0" class="rpa-progress">
          <el-progress
            :percentage="rpaProgress.percentage"
            :status="rpaProgress.percentage === 100 ? 'success' : 'active'"
          >
            <template #default="{ percentage }">
              <span class="progress-text">
                {{ rpaProgress.current }}/{{ rpaProgress.total }} ({{ percentage }}%)
              </span>
            </template>
          </el-progress>
          <div class="progress-info">
            <span v-if="rpaProgress.currentField">
              正在处理: {{ rpaProgress.currentField }}
            </span>
          </div>
        </div>

        <!-- 任务统计 -->
        <div v-if="hasLogs || hasActiveTasks" class="task-stats">
          <h4>任务统计</h4>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-statistic title="总计" :value="taskStats.total" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="运行中" :value="taskStats.running" value-style="color: #409eff" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已完成" :value="taskStats.completed" value-style="color: #67c23a" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="失败" :value="taskStats.failed" value-style="color: #f56c6c" />
            </el-col>
          </el-row>

          <!-- 完成率 -->
          <div class="completion-rate" v-if="taskStats.total > 0">
            <el-progress
              :percentage="completionRate"
              :status="completionRate === 100 ? 'success' : 'active'"
              :stroke-width="8"
            >
              <template #default="{ percentage }">
                <span class="percentage-text">完成率: {{ percentage }}%</span>
              </template>
            </el-progress>
          </div>
        </div>

        <!-- 活跃任务列表 -->
        <div v-if="rpaTasks.length > 0" class="active-tasks">
          <h4>活跃任务</h4>
          <div class="task-list">
            <div
              v-for="task in rpaTasks.slice(0, 5)"
              :key="task.taskId"
              :class="['task-item', task.status]"
            >
              <div class="task-info">
                <span class="task-id">{{ task.taskId.substring(0, 8) }}...</span>
                <span class="task-status">{{ task.status }}</span>
              </div>
              <div class="task-actions" v-if="['pending', 'running'].includes(task.status)">
                <el-button size="small" type="danger" @click="cancelTask(task.taskId)">
                  取消
                </el-button>
              </div>
              <div class="task-actions" v-else-if="task.status === 'failed'">
                <el-button size="small" type="warning" @click="retryTask(task.taskId)">
                  重试
                </el-button>
              </div>
            </div>
          </div>

          <div class="task-management" v-if="taskStats.completed > 0">
            <el-button size="small" @click="clearCompletedTasks">
              清理已完成任务
            </el-button>
            <el-button size="small" @click="exportTaskReport">
              导出任务报告
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="showRpaLogs" class="rpa-logs">
        <div class="log-header">
          <h4>RPA执行日志</h4>
          <div class="log-actions">
            <el-button size="small" @click="clearRpaLogs" :disabled="!hasLogs">
              清除日志
            </el-button>
            <el-dropdown @command="exportRpaLogs" :disabled="!hasLogs">
              <el-button size="small">
                导出日志<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="json">JSON格式</el-dropdown-item>
                  <el-dropdown-item command="txt">文本格式</el-dropdown-item>
                  <el-dropdown-item command="csv">CSV格式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="log-content" ref="logContainer">
          <div v-if="rpaLogs.length === 0" class="empty-logs">
            <el-empty description="暂无日志记录" :image-size="80" />
          </div>
          <div
            v-for="(log, index) in rpaLogs"
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 浮动RPA按钮 -->
    <el-button 
      v-if="!showRpaPanel"
      class="floating-rpa-button"
      type="primary" 
      circle 
      @click="showRpaPanel = true"
      data-testid="floating-rpa-button"
    >
      🤖
    </el-button>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, ArrowDown } from '@element-plus/icons-vue'
import { useRpa } from '@/composables/useRpa'
import { useRpaTask } from '@/composables/useRpaTask'

// 表单数据
const formData = reactive({
  name: '',
  gender: '',
  birthDate: '',
  idCard: '',
  phone: '',
  email: '',
  address: '',
  company: '',
  position: '',
  experience: '',
  salary: '',
  skills: [],
  hobbies: '',
  resume: null
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ]
}

// RPA功能
const {
  isRunning: rpaRunning,
  logs: rpaLogs,
  progress: rpaProgress,
  canStart,
  canStop,
  hasLogs,
  startFormFillTask,
  stopTask,
  clearLogs,
  generateTestData,
  validateFormData,
  exportLogs,
  getTaskStats
} = useRpa()

// RPA任务管理
const {
  isSubmitting,
  rpaTasks,
  taskStats,
  hasActiveTasks,
  completionRate,
  submitFormWithRpa,
  batchSubmitWithRpa,
  refreshTaskList,
  cancelTask,
  retryTask,
  clearCompletedTasks,
  exportTaskReport
} = useRpaTask()

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)
const showRpaPanel = ref(false)
const showRpaLogs = ref(false)
const logContainer = ref(null)

// 示例数据
const sampleData = {
  name: '张三',
  gender: 'male',
  birthDate: '1990-01-01',
  idCard: '110101199001011234',
  phone: '13800138000',
  email: '<EMAIL>',
  address: '北京市朝阳区某某街道某某小区',
  company: '某某科技有限公司',
  position: '前端开发工程师',
  experience: '3-5',
  salary: '10k-20k',
  skills: ['javascript', 'vue', 'nodejs'],
  hobbies: '喜欢编程、阅读技术书籍、打篮球'
}

// 方法
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 使用新的API提交表单并触发RPA
    await submitFormWithRpa(formData, {
      enableRpa: true,
      targetSystem: 'external-system'
    })

  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入')
  }
}

const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  addRpaLog('info', '表单已重置')
}

const fillSampleData = () => {
  Object.assign(formData, sampleData)
  ElMessage.success('示例数据填充完成')
  addRpaLog('info', '填充示例数据完成')
}

const handleResumeChange = (file) => {
  formData.resume = file
}

// RPA相关方法
const startRpaTask = async () => {
  try {
    showRpaLogs.value = true

    // 验证表单数据完整性
    const validation = validateFormData(formData)
    if (!validation.isValid) {
      ElMessage.warning('请先填充必要的表单数据')
      return
    }

    // 启动RPA表单填充任务
    await startFormFillTask(formData, {
      delay: 400,
      validate: true,
      autoSubmit: false
    })

  } catch (error) {
    ElMessage.error(`RPA任务启动失败: ${error.message}`)
  }
}

const stopRpaTask = () => {
  stopTask()
}

const clearRpaLogs = () => {
  clearLogs()
}

const exportRpaLogs = (format = 'json') => {
  exportLogs(format)
}

// 批量处理功能
const startBatchProcess = async () => {
  try {
    const testData = generateTestData(5)

    await ElMessageBox.confirm(
      `将批量处理 ${testData.length} 条测试数据，每条数据都会提交到我们系统并触发RPA录入到目标系统，是否继续？`,
      '批量处理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    showRpaLogs.value = true

    // 调用批量提交API
    await batchSubmitWithRpa(testData, {
      enableRpa: true,
      targetSystem: 'external-system'
    })

  } catch (error) {
    // 用户取消操作或处理失败
    if (error.message && error.message !== 'cancel') {
      ElMessage.error(`批量处理失败: ${error.message}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.complex-form-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
  }

  p {
    color: #7f8c8d;
    font-size: 1.1rem;
  }
}

.form-container {
  max-width: 1000px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;

  :deep(.el-card__body) {
    padding: 40px;
  }
}

.complex-form {
  .section-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #409eff;
  }

  .el-form-item {
    margin-bottom: 25px;
  }

  .submit-section {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .el-button {
      margin: 0 10px;
      min-width: 120px;
    }
  }
}

.rpa-panel {
  max-width: 1000px;
  margin: 30px auto 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 2px solid #67c23a;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    color: #67c23a;
  }
}

.rpa-controls {
  margin-bottom: 20px;

  .el-row {
    margin-bottom: 15px;
  }
}

.rpa-progress {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .progress-text {
    font-size: 12px;
    color: #606266;
  }

  .progress-info {
    margin-top: 8px;
    font-size: 13px;
    color: #909399;
  }
}

.task-stats {
  margin: 20px 0;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;

  h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
  }

  :deep(.el-statistic__content) {
    font-size: 16px;
  }

  :deep(.el-statistic__head) {
    font-size: 12px;
    color: #6c757d;
  }

  .completion-rate {
    margin-top: 15px;

    .percentage-text {
      font-size: 12px;
      color: #606266;
    }
  }
}

.active-tasks {
  margin: 20px 0;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;

  h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
  }

  .task-list {
    max-height: 200px;
    overflow-y: auto;
  }

  .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    border-left: 3px solid transparent;
    background: #f8f9fa;

    &.pending {
      border-left-color: #ffc107;
      background: rgba(255, 193, 7, 0.1);
    }

    &.running {
      border-left-color: #007bff;
      background: rgba(0, 123, 255, 0.1);
    }

    &.completed {
      border-left-color: #28a745;
      background: rgba(40, 167, 69, 0.1);
    }

    &.failed {
      border-left-color: #dc3545;
      background: rgba(220, 53, 69, 0.1);
    }

    .task-info {
      display: flex;
      flex-direction: column;

      .task-id {
        font-family: monospace;
        font-size: 12px;
        color: #6c757d;
      }

      .task-status {
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;

        &.pending { color: #ffc107; }
        &.running { color: #007bff; }
        &.completed { color: #28a745; }
        &.failed { color: #dc3545; }
      }
    }

    .task-actions {
      .el-button {
        padding: 4px 8px;
        font-size: 11px;
      }
    }
  }

  .task-management {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    gap: 10px;
  }
}

.rpa-logs {
  margin-top: 20px;

  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h4 {
      color: #606266;
      margin: 0;
    }

    .log-actions {
      display: flex;
      gap: 10px;
    }
  }

  .log-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;

    .empty-logs {
      text-align: center;
      padding: 20px;
    }
  }

  .log-item {
    display: flex;
    margin-bottom: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.info {
      border-left: 3px solid #409eff;

      .log-type {
        color: #409eff;
      }
    }

    &.success {
      border-left: 3px solid #67c23a;

      .log-type {
        color: #67c23a;
      }
    }

    &.warning {
      border-left: 3px solid #e6a23c;

      .log-type {
        color: #e6a23c;
      }
    }

    &.error {
      border-left: 3px solid #f56c6c;

      .log-type {
        color: #f56c6c;
      }
    }

    .log-time {
      min-width: 80px;
      margin-right: 10px;
      color: #909399;
      font-size: 11px;
    }

    .log-type {
      min-width: 60px;
      margin-right: 10px;
      font-weight: bold;
      font-size: 11px;
    }

    .log-message {
      flex: 1;
      color: #333;
    }
  }
}

.floating-rpa-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .complex-form-page {
    padding: 10px;
  }

  .form-container {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .form-header h1 {
    font-size: 2rem;
  }

  .rpa-controls {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }

  .floating-rpa-button {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

// 表单动画效果
.el-form-item {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.el-input, .el-select, .el-textarea {
  transition: all 0.3s ease;

  &:focus-within {
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
  }
}

// 上传组件样式
.upload-demo {
  :deep(.el-upload-dragger) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background-color: rgba(64, 158, 255, 0.05);
    }
  }
}
</style>
