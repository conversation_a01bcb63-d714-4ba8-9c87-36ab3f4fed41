<template>
  <div class="home-page">
    <div class="hero-section">
      <h1 class="hero-title">欢迎来到 AI Test Project</h1>
      <p class="hero-subtitle">探索各种前端组件和动画效果</p>

      <div class="feature-cards">
        <el-card class="feature-card" shadow="hover" @click="goToCircuitBoard">
          <div class="card-content">
            <div class="card-icon">⚡</div>
            <h3>电路板动画</h3>
            <p>体验炫酷的电路板动画效果，包含CPU、内存、数据流等元素</p>
            <el-button type="primary" class="card-button">
              查看演示
            </el-button>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">📊</div>
            <h3>数据表格</h3>
            <p>功能完整的数据表格组件，支持排序、筛选、分页等功能</p>
            <el-button type="primary" class="card-button" @click="showTable = !showTable">
              {{ showTable ? '隐藏表格' : '显示表格' }}
            </el-button>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="goToComplexForm">
          <div class="card-content">
            <div class="card-icon">📝</div>
            <h3>RPA表单录入</h3>
            <p>复杂表单自动录入系统，集成Playwright RPA工具</p>
            <el-button type="success" class="card-button">
              体验RPA
            </el-button>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="goToRpaControl">
          <div class="card-content">
            <div class="card-icon">🤖</div>
            <h3>RPA控制面板</h3>
            <p>专业的RPA任务管理和监控平台，支持批量处理和日志分析</p>
            <el-button type="warning" class="card-button">
              管理RPA
            </el-button>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover" @click="goToSystemIntegration">
          <div class="card-content">
            <div class="card-icon">🔗</div>
            <h3>系统集成演示</h3>
            <p>完整的跨系统RPA自动化流程演示，展示数据从提交到录入的全过程</p>
            <el-button type="primary" class="card-button">
              查看演示
            </el-button>
          </div>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">🎨</div>
            <h3>更多组件</h3>
            <p>持续开发中的各种UI组件和交互效果</p>
            <el-button type="info" class="card-button" disabled>
              敬请期待
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 可选的表格展示 -->
    <div v-if="showTable" class="table-section">
      <h2>数据表格演示</h2>
      <custom-table
        v-model:loading="loading"
        :tableData="tableData"
        :columns="columns"
        :showIndex="tableConfig.showIndex"
        :selection="tableConfig.selection"
        :tableHeight="tableConfig.tableHeight"
        v-model:pageSize="pageConfig.pageSize"
        v-model:currentPage="pageConfig.currentPage"
        :total="pageConfig.total"
        :pageSizes="pageConfig.pageSizes"
        @sort-change="sortChange"
        @selection-change="selectionChange"
        :search-form-data="[
          { label: '名称', prop: 'name', type: 'input' },
          {
            label: '性别',
            prop: 'sex',
            type: 'select',
            options: [{ id: 1, label: '选项1', value: '1' }],
          },
        ]"
        @search="handleSearch"
      >
        <template v-slot:status="info">
          <el-switch v-model="info.row.status">{{
            info.row.status ? "开启" : "关闭"
          }}</el-switch>
        </template>
      </custom-table>
    </div>
  </div>
</template>
<script setup>
import CustomTable from "../components/CustomTable.vue";
import { ref, watch, watchEffect, reactive } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const showTable = ref(false);

// 导航方法
const goToCircuitBoard = () => {
  router.push('/circuit-board');
};

const goToComplexForm = () => {
  router.push('/complex-form');
};

const goToRpaControl = () => {
  router.push('/rpa-control');
};

const goToSystemIntegration = () => {
  router.push('/system-integration');
};
// 表格相关
// 可以写在hooks里面
const loading = ref(false); // useHooks
const tableConfig = ref({
  showIndex: true,
  selection: true,
  tableHeight: "calc(100vh - 130px)",
});
const tableData = ref([]); // useHooks
const editFun = (row) => {};
const deleteFun = (row) => {};
const columns = ref([
  {
    label: "姓名",
    prop: "name",
    sortable: true,
  },
  {
    label: "年龄",
    prop: "age",
    sortable: true,
  },
  {
    label: "性别",
    prop: "sex",
  },
  {
    label: "是否启用",
    type: "slot",
    slotType: "status",
    width: 100,
  },
  {
    label: "操作",
    type: "slot", // 插槽
    slotType: "options", // 自定义内容
    width: 160,
    buttons: [
      {
        text: "编辑",
        fn: editFun,
      },
      {
        text: "删除",
        type: "danger",
        fn: deleteFun,
      },
    ],
  },
]);
const getTableData = () => {
  // searchParams
  loading.value = true;
  setTimeout(() => {
    tableData.value = [
      {
        id: 1,
        name: "zxc",
        age: 34,
        sex: "男",
        status: true,
        address: "北京",
      },
      {
        id: 2,
        name: "zxc",
        age: 34,
        sex: "男",
        status: false,
        address: "北京",
      },
    ];
    loading.value = false;
  }, 1000);
};
const sortChange = (info) => {}; // 排序
const selectionChange = (info) => {}; // 选择
// 分页相关
const pageConfig = reactive({
  pageSize: 10,
  currentPage: 1,
  total: 120,
  pageSizes: [10, 20, 50, 100],
});
const searchParams = ref({
  currentPage: pageConfig.currentPage,
  pageSize: pageConfig.pageSize,
});
const handleSearch = (info) => {
  if (Object.keys(info).length === 0) {
    searchParams.value = {};
  } else {
    for (const key in info) {
      if (info[key]) {
        searchParams.value[key] = info[key];
      } else {
        delete searchParams.value[key];
      }
    }
  }
  if (pageConfig.currentPage !== 1) {
    pageConfig.currentPage = 1;
  } else {
    getTableData();
  }
};
watch(
  [() => pageConfig.currentPage, () => pageConfig.pageSize],
  ([newCurrent, newSize], [oldCurrent, oldSize]) => {
    if (newCurrent || newSize) {
      getTableData();
    }
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #fff;
}

.hero-section {
  padding: 60px 20px;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #00ff41, #00ffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: title-glow 3s ease-in-out infinite alternate;
}

@keyframes title-glow {
  from {
    filter: drop-shadow(0 0 10px rgba(0, 255, 65, 0.5));
  }
  to {
    filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.5));
  }
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #ccc;
  margin-bottom: 50px;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 65, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    border-color: #00ff41;
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
  }

  :deep(.el-card__body) {
    background: transparent;
    padding: 30px;
  }
}

.card-content {
  text-align: center;
  color: #fff;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.card-content h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #00ff41;
}

.card-content p {
  color: #ccc;
  margin-bottom: 25px;
  line-height: 1.6;
}

.card-button {
  width: 100%;
}

.table-section {
  padding: 40px 20px;
  max-width: 1400px;
  margin: 0 auto;

  h2 {
    text-align: center;
    color: #00ff41;
    margin-bottom: 30px;
    font-size: 2rem;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-section {
    padding: 40px 15px;
  }
}
</style>
