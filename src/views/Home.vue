<template>
  <custom-table
    v-model:loading="loading"
    :tableData="tableData"
    :columns="columns"
    :showIndex="tableConfig.showIndex"
    :selection="tableConfig.selection"
    :tableHeight="tableConfig.tableHeight"
    v-model:pageSize="pageConfig.pageSize"
    v-model:currentPage="pageConfig.currentPage"
    :total="pageConfig.total"
    :pageSizes="pageConfig.pageSizes"
    @sort-change="sortChange"
    @selection-change="selectionChange"
    :search-form-data="[
      { label: '名称', prop: 'name', type: 'input' },
      {
        label: '性别',
        prop: 'sex',
        type: 'select',
        options: [{ id: 1, label: '选项1', value: '1' }],
      },
    ]"
    @search="handleSearch"
  >
    <!-- 自定义插槽，可以将插槽中的数据传递出来，status插槽名称，info slot中配置的属性 -->
    <template v-slot:status="info">
      <el-switch v-model="info.row.status">{{
        info.row.status ? "开启" : "关闭"
      }}</el-switch>
    </template>
  </custom-table>
</template>
<script setup>
import CustomTable from "../components/CustomTable.vue";
import { ref, watch, watchEffect, reactive } from "vue";
// 表格相关
// 可以写在hooks里面
const loading = ref(false); // useHooks
const tableConfig = ref({
  showIndex: true,
  selection: true,
  tableHeight: "calc(100vh - 130px)",
});
const tableData = ref([]); // useHooks
const editFun = (row) => {};
const deleteFun = (row) => {};
const columns = ref([
  {
    label: "姓名",
    prop: "name",
    sortable: true,
  },
  {
    label: "年龄",
    prop: "age",
    sortable: true,
  },
  {
    label: "性别",
    prop: "sex",
  },
  {
    label: "是否启用",
    type: "slot",
    slotType: "status",
    width: 100,
  },
  {
    label: "操作",
    type: "slot", // 插槽
    slotType: "options", // 自定义内容
    width: 160,
    buttons: [
      {
        text: "编辑",
        fn: editFun,
      },
      {
        text: "删除",
        type: "danger",
        fn: deleteFun,
      },
    ],
  },
]);
const getTableData = () => {
  // searchParams
  loading.value = true;
  setTimeout(() => {
    tableData.value = [
      {
        id: 1,
        name: "zxc",
        age: 34,
        sex: "男",
        status: true,
        address: "北京",
      },
      {
        id: 2,
        name: "zxc",
        age: 34,
        sex: "男",
        status: false,
        address: "北京",
      },
    ];
    loading.value = false;
  }, 1000);
};
const sortChange = (info) => {}; // 排序
const selectionChange = (info) => {}; // 选择
// 分页相关
const pageConfig = reactive({
  pageSize: 10,
  currentPage: 1,
  total: 120,
  pageSizes: [10, 20, 50, 100],
});
const searchParams = ref({
  currentPage: pageConfig.currentPage,
  pageSize: pageConfig.pageSize,
});
const handleSearch = (info) => {
  if (Object.keys(info).length === 0) {
    searchParams.value = {};
  } else {
    for (const key in info) {
      if (info[key]) {
        searchParams.value[key] = info[key];
      } else {
        delete searchParams.value[key];
      }
    }
  }
  if (pageConfig.currentPage !== 1) {
    pageConfig.currentPage = 1;
  } else {
    getTableData();
  }
};
watch(
  [() => pageConfig.currentPage, () => pageConfig.pageSize],
  ([newCurrent, newSize], [oldCurrent, oldSize]) => {
    if (newCurrent || newSize) {
      getTableData();
    }
  },
  {
    immediate: true,
  }
);
</script>
