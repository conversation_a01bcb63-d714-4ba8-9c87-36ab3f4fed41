<template>
  <div class="circuit-board-demo">
    <div class="demo-header">
      <h1>电路板动画组件演示</h1>
      <div class="controls">
        <el-button type="primary" @click="startAnimation">启动动画</el-button>
        <el-button type="warning" @click="stopAnimation">停止动画</el-button>
        <el-button type="info" @click="toggleSize">切换尺寸</el-button>
      </div>
    </div>
    
    <div class="demo-content">
      <div class="circuit-wrapper">
        <CircuitBoard 
          ref="circuitBoardRef"
          :width="boardWidth" 
          :height="boardHeight"
          :animated="isAnimated"
        />
      </div>
      
      <div class="info-panel">
        <el-card class="info-card">
          <template #header>
            <span>组件信息</span>
          </template>
          <div class="info-item">
            <span class="label">宽度:</span>
            <span class="value">{{ boardWidth }}px</span>
          </div>
          <div class="info-item">
            <span class="label">高度:</span>
            <span class="value">{{ boardHeight }}px</span>
          </div>
          <div class="info-item">
            <span class="label">动画状态:</span>
            <span class="value" :class="{ active: isAnimated }">
              {{ isAnimated ? '运行中' : '已停止' }}
            </span>
          </div>
        </el-card>
        
        <el-card class="features-card">
          <template #header>
            <span>功能特性</span>
          </template>
          <ul class="features-list">
            <li>✨ SVG矢量图形，支持任意缩放</li>
            <li>🔥 多种动画效果：发光、脉冲、数据流</li>
            <li>⚡ 电路元件：CPU、RAM、SSD、电阻、电容</li>
            <li>🌟 连接点闪烁效果</li>
            <li>🎯 可控制的动画开关</li>
            <li>📱 响应式设计</li>
          </ul>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CircuitBoard from '@/components/CircuitBoard.vue'

// 响应式数据
const circuitBoardRef = ref(null)
const isAnimated = ref(true)
const boardWidth = ref(800)
const boardHeight = ref(600)

const sizes = [
  { width: 800, height: 600 },
  { width: 1000, height: 750 },
  { width: 600, height: 450 }
]
let currentSizeIndex = 0

// 方法
const startAnimation = () => {
  isAnimated.value = true
  if (circuitBoardRef.value) {
    circuitBoardRef.value.startAnimation()
  }
}

const stopAnimation = () => {
  isAnimated.value = false
  if (circuitBoardRef.value) {
    circuitBoardRef.value.stopAnimation()
  }
}

const toggleSize = () => {
  currentSizeIndex = (currentSizeIndex + 1) % sizes.length
  const newSize = sizes[currentSizeIndex]
  boardWidth.value = newSize.width
  boardHeight.value = newSize.height
}
</script>

<style lang="scss" scoped>
.circuit-board-demo {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #fff;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #00ff41, #00ffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
  }
}

.demo-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: center;
}

.circuit-wrapper {
  flex: 1;
  min-width: 600px;
  display: flex;
  justify-content: center;
}

.info-panel {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card, .features-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 65, 0.3);
  
  :deep(.el-card__header) {
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
    font-weight: bold;
  }
  
  :deep(.el-card__body) {
    background: transparent;
    color: #fff;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  
  .label {
    color: #ccc;
  }
  
  .value {
    color: #00ff41;
    font-weight: bold;
    
    &.active {
      animation: status-pulse 2s ease-in-out infinite;
    }
  }
}

@keyframes status-pulse {
  0%, 100% {
    color: #00ff41;
  }
  50% {
    color: #00ffff;
  }
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .demo-content {
    flex-direction: column;
    align-items: center;
  }
  
  .info-panel {
    flex: none;
    width: 100%;
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .circuit-wrapper {
    min-width: auto;
    width: 100%;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
