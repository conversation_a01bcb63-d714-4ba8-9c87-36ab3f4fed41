<template>
  <div class="system-integration">
    <div class="page-header">
      <h1>跨系统RPA集成演示</h1>
      <p>展示从Vue3系统提交数据到目标系统的完整RPA自动化流程</p>
    </div>

    <!-- 流程图 -->
    <el-card class="flow-diagram">
      <template #header>
        <span>系统集成流程</span>
      </template>
      
      <div class="flow-steps">
        <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3>Vue3系统</h3>
            <p>用户填写表单并提交</p>
          </div>
        </div>
        
        <div class="flow-arrow">→</div>
        
        <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3>API处理</h3>
            <p>数据验证和存储</p>
          </div>
        </div>
        
        <div class="flow-arrow">→</div>
        
        <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3>RPA触发</h3>
            <p>创建自动化任务</p>
          </div>
        </div>
        
        <div class="flow-arrow">→</div>
        
        <div class="step" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
          <div class="step-number">4</div>
          <div class="step-content">
            <h3>目标系统</h3>
            <p>自动录入数据</p>
          </div>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 左侧：表单提交区域 -->
      <el-col :span="12">
        <el-card class="form-section">
          <template #header>
            <div class="card-header">
              <span>步骤1: 表单数据提交</span>
              <el-tag :type="formStatus === 'success' ? 'success' : 'info'">
                {{ formStatus === 'success' ? '已提交' : '待提交' }}
              </el-tag>
            </div>
          </template>

          <el-form :model="demoFormData" label-width="100px" class="demo-form">
            <el-form-item label="姓名">
              <el-input v-model="demoFormData.name" placeholder="请输入姓名" />
            </el-form-item>
            
            <el-form-item label="邮箱">
              <el-input v-model="demoFormData.email" placeholder="请输入邮箱" />
            </el-form-item>
            
            <el-form-item label="手机号">
              <el-input v-model="demoFormData.phone" placeholder="请输入手机号" />
            </el-form-item>
            
            <el-form-item label="公司">
              <el-input v-model="demoFormData.company" placeholder="请输入公司名称" />
            </el-form-item>
            
            <el-form-item label="职位">
              <el-input v-model="demoFormData.position" placeholder="请输入职位" />
            </el-form-item>
            
            <el-form-item label="目标系统">
              <el-select v-model="demoFormData.targetSystem" style="width: 100%">
                <el-option label="外部CRM系统" value="external-crm" />
                <el-option label="HR管理系统" value="hr-system" />
                <el-option label="客户管理系统" value="customer-system" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="submitDemoForm" 
                :loading="isSubmitting"
                :disabled="!canSubmit"
              >
                提交并触发RPA
              </el-button>
              <el-button @click="fillDemoData">
                填充示例数据
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧：RPA任务监控 -->
      <el-col :span="12">
        <el-card class="monitoring-section">
          <template #header>
            <div class="card-header">
              <span>步骤2-4: RPA任务监控</span>
              <el-tag :type="getTaskStatusType()" v-if="currentTask">
                {{ getTaskStatusText() }}
              </el-tag>
            </div>
          </template>

          <!-- 任务信息 -->
          <div v-if="currentTask" class="task-info">
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="任务ID">
                {{ currentTask.id }}
              </el-descriptions-item>
              <el-descriptions-item label="目标系统">
                {{ currentTask.targetSystem }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getTaskStatusType()">
                  {{ getTaskStatusText() }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(currentTask.createdAt) }}
              </el-descriptions-item>
              <el-descriptions-item label="尝试次数" v-if="currentTask.attempts > 0">
                {{ currentTask.attempts }} / {{ currentTask.maxAttempts }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 任务操作 -->
            <div class="task-actions">
              <el-button 
                v-if="currentTask.status === 'running'" 
                type="danger" 
                size="small"
                @click="cancelCurrentTask"
              >
                取消任务
              </el-button>
              <el-button 
                v-if="currentTask.status === 'failed'" 
                type="warning" 
                size="small"
                @click="retryCurrentTask"
              >
                重试任务
              </el-button>
            </div>
          </div>

          <!-- 无任务状态 -->
          <div v-else class="no-task">
            <el-empty description="暂无活跃任务" :image-size="80" />
          </div>
        </el-card>

        <!-- 任务统计 -->
        <el-card class="stats-section">
          <template #header>
            <span>任务统计</span>
          </template>
          
          <el-row :gutter="15">
            <el-col :span="8">
              <el-statistic title="总计" :value="taskStats.total" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="成功" :value="taskStats.completed" value-style="color: #67c23a" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="失败" :value="taskStats.failed" value-style="color: #f56c6c" />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 执行日志 -->
    <el-card class="logs-section">
      <template #header>
        <div class="card-header">
          <span>执行日志</span>
          <el-button type="text" @click="clearLogs" :disabled="logs.length === 0">
            清除日志
          </el-button>
        </div>
      </template>

      <div class="logs-container">
        <div v-if="logs.length === 0" class="empty-logs">
          <el-empty description="暂无执行日志" />
        </div>
        <div v-else class="log-list">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 系统配置 -->
    <el-card class="config-section">
      <template #header>
        <span>系统配置</span>
      </template>
      
      <el-form :model="systemConfig" label-width="120px" class="config-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="源系统URL">
              <el-input v-model="systemConfig.sourceSystem" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标系统URL">
              <el-input v-model="systemConfig.targetSystem" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大并发数">
              <el-input-number v-model="systemConfig.maxConcurrent" :min="1" :max="10" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重试次数">
              <el-input-number v-model="systemConfig.retryAttempts" :min="1" :max="5" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="updateConfig">
            更新配置
          </el-button>
          <el-button @click="resetConfig">
            重置配置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useRpaTask } from '@/composables/useRpaTask'
import { globalTaskQueue } from '@/utils/taskQueue'

// 响应式数据
const currentStep = ref(1)
const formStatus = ref('pending')
const currentTask = ref(null)
const logs = ref([])

// 表单数据
const demoFormData = reactive({
  name: '',
  email: '',
  phone: '',
  company: '',
  position: '',
  targetSystem: 'external-crm'
})

// 系统配置
const systemConfig = reactive({
  sourceSystem: 'http://localhost:5173',
  targetSystem: 'https://target-system.com',
  maxConcurrent: 3,
  retryAttempts: 3
})

// RPA任务管理
const {
  isSubmitting,
  taskStats,
  submitFormWithRpa
} = useRpaTask()

// 计算属性
const canSubmit = computed(() => {
  return demoFormData.name && demoFormData.email && demoFormData.phone
})

// 方法
const submitDemoForm = async () => {
  try {
    currentStep.value = 2
    addLog('info', '开始提交表单数据...')
    
    // 提交表单并触发RPA
    const result = await submitFormWithRpa(demoFormData, {
      enableRpa: true,
      targetSystem: demoFormData.targetSystem
    })
    
    if (result.success) {
      formStatus.value = 'success'
      currentStep.value = 3
      addLog('success', '表单提交成功，RPA任务已创建')
      
      // 模拟RPA任务执行
      simulateRpaExecution()
    }
    
  } catch (error) {
    addLog('error', `提交失败: ${error.message}`)
    ElMessage.error('提交失败')
  }
}

const simulateRpaExecution = () => {
  // 创建模拟任务
  const task = {
    id: `demo_${Date.now()}`,
    targetSystem: demoFormData.targetSystem,
    status: 'running',
    attempts: 0,
    maxAttempts: 3,
    createdAt: new Date().toISOString()
  }
  
  currentTask.value = task
  currentStep.value = 4
  addLog('info', `RPA任务开始执行: ${task.id}`)
  
  // 模拟执行过程
  setTimeout(() => {
    const success = Math.random() > 0.3 // 70%成功率
    
    if (success) {
      task.status = 'completed'
      addLog('success', 'RPA任务执行成功，数据已录入目标系统')
      ElNotification({
        title: 'RPA任务完成',
        message: '数据已成功录入到目标系统',
        type: 'success'
      })
    } else {
      task.status = 'failed'
      task.attempts = 1
      addLog('error', 'RPA任务执行失败，将自动重试')
    }
  }, 3000)
}

const fillDemoData = () => {
  Object.assign(demoFormData, {
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138000',
    company: '某某科技有限公司',
    position: '软件工程师'
  })
  addLog('info', '已填充示例数据')
}

const cancelCurrentTask = () => {
  if (currentTask.value) {
    currentTask.value.status = 'cancelled'
    addLog('warning', '任务已取消')
    ElMessage.warning('任务已取消')
  }
}

const retryCurrentTask = () => {
  if (currentTask.value) {
    currentTask.value.status = 'running'
    currentTask.value.attempts += 1
    addLog('info', '任务重试中...')
    
    // 模拟重试
    setTimeout(() => {
      const success = Math.random() > 0.5
      if (success) {
        currentTask.value.status = 'completed'
        addLog('success', '重试成功')
      } else {
        currentTask.value.status = 'failed'
        addLog('error', '重试失败')
      }
    }, 2000)
  }
}

const getTaskStatusType = () => {
  if (!currentTask.value) return 'info'
  
  const statusMap = {
    pending: 'warning',
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  
  return statusMap[currentTask.value.status] || 'info'
}

const getTaskStatusText = () => {
  if (!currentTask.value) return ''
  
  const statusMap = {
    pending: '等待中',
    running: '执行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  
  return statusMap[currentTask.value.status] || '未知'
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const addLog = (type, message) => {
  const log = {
    type,
    message,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  }
  logs.value.push(log)
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-50)
  }
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清除')
}

const updateConfig = () => {
  // 更新全局配置
  globalTaskQueue.options.maxConcurrent = systemConfig.maxConcurrent
  globalTaskQueue.options.retryAttempts = systemConfig.retryAttempts
  
  addLog('info', '系统配置已更新')
  ElMessage.success('配置更新成功')
}

const resetConfig = () => {
  Object.assign(systemConfig, {
    sourceSystem: 'http://localhost:5173',
    targetSystem: 'https://target-system.com',
    maxConcurrent: 3,
    retryAttempts: 3
  })
  addLog('info', '系统配置已重置')
}

// 生命周期
onMounted(() => {
  addLog('info', '系统集成演示页面已加载')
})
</script>

<style lang="scss" scoped>
.system-integration {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
  }

  p {
    color: #7f8c8d;
    font-size: 1.1rem;
  }
}

.flow-diagram {
  margin-bottom: 30px;

  .flow-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 12px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    min-width: 150px;

    &.active {
      border-color: #007bff;
      background: rgba(0, 123, 255, 0.1);
      transform: scale(1.05);
    }

    &.completed {
      border-color: #28a745;
      background: rgba(40, 167, 69, 0.1);
    }

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #6c757d;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-bottom: 10px;
    }

    &.active .step-number {
      background: #007bff;
    }

    &.completed .step-number {
      background: #28a745;
    }

    .step-content {
      text-align: center;

      h3 {
        margin: 0 0 5px 0;
        font-size: 1.1rem;
        color: #495057;
      }

      p {
        margin: 0;
        font-size: 0.9rem;
        color: #6c757d;
      }
    }
  }

  .flow-arrow {
    font-size: 1.5rem;
    color: #6c757d;
    font-weight: bold;
  }
}

.form-section, .monitoring-section, .stats-section,
.logs-section, .config-section {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
  }
}

.demo-form, .config-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.task-info {
  .task-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
  }
}

.no-task {
  text-align: center;
  padding: 40px;
}

.stats-section {
  margin-top: 20px;

  :deep(.el-statistic__content) {
    font-size: 18px;
  }

  :deep(.el-statistic__head) {
    font-size: 12px;
    color: #6c757d;
  }
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;

  .empty-logs {
    text-align: center;
    padding: 40px;
  }

  .log-list {
    font-family: 'Courier New', monospace;
    font-size: 13px;
  }

  .log-item {
    display: flex;
    padding: 8px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    border-left: 3px solid transparent;

    &.info {
      border-left-color: #17a2b8;
      background: rgba(23, 162, 184, 0.05);

      .log-type {
        color: #17a2b8;
      }
    }

    &.success {
      border-left-color: #28a745;
      background: rgba(40, 167, 69, 0.05);

      .log-type {
        color: #28a745;
      }
    }

    &.warning {
      border-left-color: #ffc107;
      background: rgba(255, 193, 7, 0.05);

      .log-type {
        color: #ffc107;
      }
    }

    &.error {
      border-left-color: #dc3545;
      background: rgba(220, 53, 69, 0.05);

      .log-type {
        color: #dc3545;
      }
    }

    .log-time {
      min-width: 80px;
      margin-right: 10px;
      color: #6c757d;
      font-size: 11px;
    }

    .log-type {
      min-width: 60px;
      margin-right: 10px;
      font-weight: bold;
      font-size: 11px;
    }

    .log-message {
      flex: 1;
      color: #495057;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .system-integration {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .flow-steps {
    flex-direction: column;

    .flow-arrow {
      transform: rotate(90deg);
    }
  }
}

@media (max-width: 768px) {
  .system-integration {
    padding: 10px;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .step {
    min-width: 120px;
    padding: 15px;
  }
}
</style>
