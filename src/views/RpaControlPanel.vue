<template>
  <div class="rpa-control-panel">
    <div class="panel-header">
      <h1>RPA自动化控制面板</h1>
      <p>管理和监控RPA任务执行</p>
    </div>

    <!-- 任务配置区域 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="task-config-card">
          <template #header>
            <div class="card-header">
              <span>任务配置</span>
              <el-button type="text" @click="loadTemplate">加载模板</el-button>
            </div>
          </template>

          <el-form :model="taskConfig" label-width="120px" class="task-form">
            <el-form-item label="任务名称">
              <el-input v-model="taskConfig.name" placeholder="请输入任务名称" />
            </el-form-item>

            <el-form-item label="目标URL">
              <el-input v-model="taskConfig.url" placeholder="http://localhost:5173/complex-form" />
            </el-form-item>

            <el-form-item label="浏览器类型">
              <el-select v-model="taskConfig.browserType" style="width: 100%">
                <el-option label="Chrome" value="chromium" />
                <el-option label="Firefox" value="firefox" />
                <el-option label="Safari" value="webkit" />
              </el-select>
            </el-form-item>

            <el-form-item label="执行模式">
              <el-radio-group v-model="taskConfig.headless">
                <el-radio :label="false">可视化模式</el-radio>
                <el-radio :label="true">无头模式</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="操作延迟">
              <el-slider 
                v-model="taskConfig.slowMo" 
                :min="0" 
                :max="2000" 
                :step="100"
                show-input
                :format-tooltip="(val) => `${val}ms`"
              />
            </el-form-item>

            <el-form-item label="任务选项">
              <el-checkbox-group v-model="taskConfig.options">
                <el-checkbox label="autoSubmit">自动提交表单</el-checkbox>
                <el-checkbox label="takeScreenshot">执行截图</el-checkbox>
                <el-checkbox label="validate">数据验证</el-checkbox>
                <el-checkbox label="autoClose">完成后关闭浏览器</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 表单数据配置 -->
        <el-card class="form-data-card">
          <template #header>
            <div class="card-header">
              <span>表单数据</span>
              <div>
                <el-button type="text" @click="generateSampleData">生成示例数据</el-button>
                <el-button type="text" @click="importData">导入数据</el-button>
              </div>
            </div>
          </template>

          <el-form :model="formData" label-width="100px" class="form-data">
            <el-row :gutter="15">
              <el-col :span="12">
                <el-form-item label="姓名">
                  <el-input v-model="formData.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="formData.gender" style="width: 100%">
                    <el-option label="男" value="male" />
                    <el-option label="女" value="female" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="15">
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input v-model="formData.phone" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="formData.email" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="15">
              <el-col :span="12">
                <el-form-item label="公司">
                  <el-input v-model="formData.company" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职位">
                  <el-input v-model="formData.position" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="技能">
              <el-checkbox-group v-model="formData.skills">
                <el-checkbox label="javascript" value="javascript">JavaScript</el-checkbox>
                <el-checkbox label="vue" value="vue">Vue.js</el-checkbox>
                <el-checkbox label="react" value="react">React</el-checkbox>
                <el-checkbox label="nodejs" value="nodejs">Node.js</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 控制和监控区域 -->
      <el-col :span="8">
        <el-card class="control-card">
          <template #header>
            <span>任务控制</span>
          </template>

          <div class="control-buttons">
            <el-button 
              type="primary" 
              size="large"
              @click="startTask" 
              :loading="isRunning"
              :disabled="!canStart"
              style="width: 100%; margin-bottom: 15px;"
            >
              {{ isRunning ? '执行中...' : '启动RPA任务' }}
            </el-button>

            <el-button 
              type="danger" 
              size="large"
              @click="stopTask" 
              :disabled="!isRunning"
              style="width: 100%; margin-bottom: 15px;"
            >
              停止任务
            </el-button>

            <el-button 
              type="info" 
              size="large"
              @click="testConnection" 
              :disabled="isRunning"
              style="width: 100%; margin-bottom: 15px;"
            >
              测试连接
            </el-button>
          </div>

          <!-- 任务状态 -->
          <div class="task-status">
            <h4>任务状态</h4>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="状态">
                <el-tag :type="isRunning ? 'success' : 'info'">
                  {{ isRunning ? '运行中' : '空闲' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="当前任务">
                {{ currentTask || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="执行时间">
                {{ executionTime }}
              </el-descriptions-item>
              <el-descriptions-item label="成功率">
                {{ successRate }}%
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 快速操作 -->
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>

          <div class="quick-actions">
            <el-button 
              v-for="template in quickTemplates" 
              :key="template.name"
              type="text" 
              @click="applyQuickTemplate(template)"
              class="quick-action-btn"
            >
              {{ template.name }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 执行日志 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>执行日志</span>
          <div>
            <el-button type="text" @click="clearLogs">清除日志</el-button>
            <el-button type="text" @click="exportLogs">导出日志</el-button>
          </div>
        </div>
      </template>

      <div class="logs-container">
        <div v-if="logs.length === 0" class="empty-logs">
          <el-empty description="暂无执行日志" />
        </div>
        <div v-else class="log-list">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 任务配置
const taskConfig = reactive({
  name: 'RPA表单填充任务',
  url: 'http://localhost:5173/complex-form',
  browserType: 'chromium',
  headless: false,
  slowMo: 300,
  options: ['takeScreenshot', 'validate', 'autoClose']
})

// 表单数据
const formData = reactive({
  name: '',
  gender: '',
  phone: '',
  email: '',
  company: '',
  position: '',
  skills: []
})

// 状态管理
const isRunning = ref(false)
const currentTask = ref('')
const executionTime = ref('00:00:00')
const successRate = ref(100)
const logs = ref([])

// 计算属性
const canStart = computed(() => !isRunning.value && taskConfig.url && formData.name)

// 快速模板
const quickTemplates = [
  {
    name: '前端工程师',
    data: {
      name: '张三',
      gender: 'male',
      phone: '13800138000',
      email: '<EMAIL>',
      company: '某某科技',
      position: '前端工程师',
      skills: ['javascript', 'vue', 'react']
    }
  },
  {
    name: '后端工程师',
    data: {
      name: '李四',
      gender: 'female',
      phone: '13900139000',
      email: '<EMAIL>',
      company: '某某互联网',
      position: '后端工程师',
      skills: ['nodejs', 'python']
    }
  }
]

// 方法
const startTask = async () => {
  try {
    isRunning.value = true
    currentTask.value = taskConfig.name
    
    addLog('info', '开始执行RPA任务')
    addLog('info', `目标URL: ${taskConfig.url}`)
    addLog('info', `浏览器类型: ${taskConfig.browserType}`)
    
    // 这里应该调用实际的Playwright RPA执行器
    // 由于在浏览器环境中无法直接使用Playwright，这里使用模拟
    await simulateRpaExecution()
    
    addLog('success', 'RPA任务执行完成')
    ElMessage.success('RPA任务执行成功')
    
  } catch (error) {
    addLog('error', `RPA任务执行失败: ${error.message}`)
    ElMessage.error('RPA任务执行失败')
  } finally {
    isRunning.value = false
    currentTask.value = ''
  }
}

const stopTask = () => {
  isRunning.value = false
  currentTask.value = ''
  addLog('warning', 'RPA任务已停止')
  ElMessage.warning('RPA任务已停止')
}

const testConnection = async () => {
  try {
    addLog('info', '测试连接中...')
    
    // 模拟连接测试
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    addLog('success', '连接测试成功')
    ElMessage.success('连接测试成功')
  } catch (error) {
    addLog('error', '连接测试失败')
    ElMessage.error('连接测试失败')
  }
}

const simulateRpaExecution = async () => {
  const steps = [
    '启动浏览器',
    '导航到目标页面',
    '等待页面加载',
    '填充姓名字段',
    '选择性别',
    '填充手机号',
    '填充邮箱',
    '填充公司信息',
    '填充职位信息',
    '选择技能标签',
    '执行截图',
    '验证数据',
    '关闭浏览器'
  ]

  for (let i = 0; i < steps.length; i++) {
    if (!isRunning.value) break
    
    addLog('info', steps[i])
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

const generateSampleData = () => {
  Object.assign(formData, {
    name: '张三',
    gender: 'male',
    phone: '13800138000',
    email: '<EMAIL>',
    company: '某某科技有限公司',
    position: '前端开发工程师',
    skills: ['javascript', 'vue']
  })
  
  addLog('info', '已生成示例数据')
  ElMessage.success('示例数据生成完成')
}

const applyQuickTemplate = (template) => {
  Object.assign(formData, template.data)
  addLog('info', `应用模板: ${template.name}`)
  ElMessage.success(`已应用${template.name}模板`)
}

const loadTemplate = () => {
  ElMessage.info('模板加载功能开发中')
}

const importData = () => {
  ElMessage.info('数据导入功能开发中')
}

const addLog = (type, message) => {
  const log = {
    type,
    message,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  }
  logs.value.push(log)
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清除')
}

const exportLogs = () => {
  const logText = logs.value
    .map(log => `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`)
    .join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `rpa-logs-${Date.now()}.txt`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志导出完成')
}

// 生命周期
onMounted(() => {
  addLog('info', 'RPA控制面板已初始化')
})
</script>
