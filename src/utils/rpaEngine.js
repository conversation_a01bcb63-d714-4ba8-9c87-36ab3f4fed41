/**
 * RPA引擎 - 基于Playwright的自动化表单录入工具
 * 支持表单自动填充、验证和提交
 */

class RpaEngine {
  constructor() {
    this.isRunning = false
    this.currentTask = null
    this.logs = []
    this.callbacks = {
      onLog: null,
      onProgress: null,
      onComplete: null,
      onError: null
    }
  }

  /**
   * 设置回调函数
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }

  /**
   * 添加日志
   */
  addLog(type, message) {
    const log = {
      type,
      message,
      timestamp: new Date().toISOString(),
      time: new Date().toLocaleTimeString()
    }
    this.logs.push(log)
    
    if (this.callbacks.onLog) {
      this.callbacks.onLog(log)
    }
  }

  /**
   * 启动RPA任务
   */
  async startTask(taskConfig) {
    if (this.isRunning) {
      throw new Error('RPA任务正在运行中')
    }

    this.isRunning = true
    this.currentTask = taskConfig
    this.logs = []

    try {
      this.addLog('info', '开始执行RPA任务')
      
      // 根据任务类型执行不同的流程
      switch (taskConfig.type) {
        case 'form-fill':
          await this.executeFormFillTask(taskConfig)
          break
        case 'batch-process':
          await this.executeBatchProcessTask(taskConfig)
          break
        case 'data-extraction':
          await this.executeDataExtractionTask(taskConfig)
          break
        default:
          throw new Error(`不支持的任务类型: ${taskConfig.type}`)
      }

      this.addLog('success', 'RPA任务执行完成')
      if (this.callbacks.onComplete) {
        this.callbacks.onComplete()
      }
    } catch (error) {
      this.addLog('error', `RPA任务执行失败: ${error.message}`)
      if (this.callbacks.onError) {
        this.callbacks.onError(error)
      }
      throw error
    } finally {
      this.isRunning = false
      this.currentTask = null
    }
  }

  /**
   * 停止RPA任务
   */
  stopTask() {
    if (this.isRunning) {
      this.isRunning = false
      this.addLog('warning', 'RPA任务已被用户停止')
    }
  }

  /**
   * 执行表单填充任务
   */
  async executeFormFillTask(taskConfig) {
    const { formData, options = {} } = taskConfig
    const { delay = 300, validate = true } = options

    this.addLog('info', '开始表单填充流程')

    // 模拟表单字段填充
    const fields = Object.keys(formData)
    for (let i = 0; i < fields.length; i++) {
      if (!this.isRunning) break

      const field = fields[i]
      const value = formData[field]

      this.addLog('info', `填充字段: ${field}`)
      
      // 模拟填充延迟
      await this.sleep(delay)
      
      // 触发进度回调
      if (this.callbacks.onProgress) {
        this.callbacks.onProgress({
          current: i + 1,
          total: fields.length,
          field,
          value
        })
      }
    }

    if (validate) {
      this.addLog('info', '开始表单验证')
      await this.validateForm(formData)
    }

    this.addLog('success', '表单填充完成')
  }

  /**
   * 执行批量处理任务
   */
  async executeBatchProcessTask(taskConfig) {
    const { dataList, batchSize = 10 } = taskConfig
    
    this.addLog('info', `开始批量处理，共${dataList.length}条数据`)

    for (let i = 0; i < dataList.length; i += batchSize) {
      if (!this.isRunning) break

      const batch = dataList.slice(i, i + batchSize)
      this.addLog('info', `处理批次 ${Math.floor(i / batchSize) + 1}，包含${batch.length}条数据`)

      // 并行处理批次数据
      await Promise.all(
        batch.map(async (data, index) => {
          await this.processSingleRecord(data, i + index)
        })
      )

      // 批次间延迟
      await this.sleep(1000)
    }

    this.addLog('success', '批量处理完成')
  }

  /**
   * 执行数据提取任务
   */
  async executeDataExtractionTask(taskConfig) {
    const { selectors, outputFormat = 'json' } = taskConfig
    
    this.addLog('info', '开始数据提取')

    const extractedData = {}
    
    for (const [key, selector] of Object.entries(selectors)) {
      this.addLog('info', `提取数据: ${key}`)
      
      // 模拟数据提取
      await this.sleep(200)
      extractedData[key] = `extracted_${key}_value`
    }

    this.addLog('success', `数据提取完成，格式: ${outputFormat}`)
    return extractedData
  }

  /**
   * 处理单条记录
   */
  async processSingleRecord(data, index) {
    this.addLog('info', `处理记录 ${index + 1}: ${data.name || data.id || '未知'}`)
    
    // 模拟处理时间
    await this.sleep(Math.random() * 500 + 200)
    
    // 模拟成功率（95%）
    if (Math.random() < 0.05) {
      throw new Error(`记录 ${index + 1} 处理失败`)
    }
  }

  /**
   * 表单验证
   */
  async validateForm(formData) {
    const validationRules = {
      name: (value) => value && value.length > 0,
      email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      phone: (value) => /^1[3-9]\d{9}$/.test(value),
      idCard: (value) => /^\d{17}[\dX]$/.test(value)
    }

    for (const [field, rule] of Object.entries(validationRules)) {
      if (formData[field] && !rule(formData[field])) {
        throw new Error(`字段 ${field} 验证失败`)
      }
    }

    await this.sleep(500)
    this.addLog('success', '表单验证通过')
  }

  /**
   * 延迟函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取任务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentTask: this.currentTask,
      logs: this.logs
    }
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
  }
}

/**
 * RPA任务配置生成器
 */
export class RpaTaskBuilder {
  constructor() {
    this.config = {}
  }

  /**
   * 设置任务类型
   */
  setType(type) {
    this.config.type = type
    return this
  }

  /**
   * 设置表单数据
   */
  setFormData(formData) {
    this.config.formData = formData
    return this
  }

  /**
   * 设置选项
   */
  setOptions(options) {
    this.config.options = { ...this.config.options, ...options }
    return this
  }

  /**
   * 设置批量数据
   */
  setBatchData(dataList, batchSize = 10) {
    this.config.dataList = dataList
    this.config.batchSize = batchSize
    return this
  }

  /**
   * 设置数据提取选择器
   */
  setSelectors(selectors) {
    this.config.selectors = selectors
    return this
  }

  /**
   * 构建任务配置
   */
  build() {
    return { ...this.config }
  }
}

/**
 * RPA工具类 - 提供常用的RPA操作
 */
export class RpaUtils {
  /**
   * 生成随机延迟
   */
  static randomDelay(min = 100, max = 500) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  /**
   * 生成测试数据
   */
  static generateTestData(count = 10) {
    const testData = []
    const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
    const companies = ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '滴滴', '京东', '网易']
    
    for (let i = 0; i < count; i++) {
      testData.push({
        id: i + 1,
        name: names[i % names.length] + (i + 1),
        gender: Math.random() > 0.5 ? 'male' : 'female',
        phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
        email: `user${i + 1}@example.com`,
        company: companies[i % companies.length],
        position: '软件工程师'
      })
    }
    
    return testData
  }

  /**
   * 验证表单数据
   */
  static validateFormData(formData) {
    const errors = []
    
    if (!formData.name) errors.push('姓名不能为空')
    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('邮箱格式不正确')
    }
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.push('手机号格式不正确')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// 创建全局RPA引擎实例
export const rpaEngine = new RpaEngine()

export default RpaEngine
