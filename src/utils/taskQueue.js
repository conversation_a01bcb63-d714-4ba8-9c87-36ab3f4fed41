/**
 * RPA任务队列管理系统
 * 处理任务的创建、调度、执行和状态管理
 */

import { RpaTaskExecutor } from '@/rpa/crossSystemRpa'

export class TaskQueue {
  constructor(options = {}) {
    this.tasks = new Map()
    this.executors = new Map()
    this.options = {
      maxConcurrent: 3,
      retryAttempts: 3,
      retryDelay: 5000,
      taskTimeout: 300000, // 5分钟
      ...options
    }
    this.isProcessing = false
    this.stats = {
      total: 0,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0
    }
    this.listeners = new Map()
  }

  /**
   * 添加任务到队列
   */
  addTask(taskData) {
    const task = {
      id: taskData.taskId || `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      formId: taskData.formId,
      formData: taskData.formData,
      targetSystem: taskData.targetSystem || 'external-system',
      priority: taskData.priority || 'normal',
      status: 'pending',
      attempts: 0,
      maxAttempts: this.options.retryAttempts,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      logs: [],
      result: null,
      error: null
    }

    this.tasks.set(task.id, task)
    this.updateStats()
    this.emit('task-added', task)
    
    console.log(`任务已添加到队列: ${task.id}`)
    
    // 自动开始处理队列
    this.processQueue()
    
    return task.id
  }

  /**
   * 处理任务队列
   */
  async processQueue() {
    if (this.isProcessing) return
    
    this.isProcessing = true
    
    try {
      while (this.hasAvailableSlots() && this.hasPendingTasks()) {
        const task = this.getNextTask()
        if (task) {
          this.executeTask(task)
        }
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 检查是否有可用的执行槽位
   */
  hasAvailableSlots() {
    return this.getRunningTasksCount() < this.options.maxConcurrent
  }

  /**
   * 检查是否有待处理的任务
   */
  hasPendingTasks() {
    return Array.from(this.tasks.values()).some(task => task.status === 'pending')
  }

  /**
   * 获取下一个要执行的任务
   */
  getNextTask() {
    const pendingTasks = Array.from(this.tasks.values())
      .filter(task => task.status === 'pending')
      .sort((a, b) => {
        // 按优先级排序
        const priorityOrder = { high: 3, normal: 2, low: 1 }
        const aPriority = priorityOrder[a.priority] || 2
        const bPriority = priorityOrder[b.priority] || 2
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority
        }
        
        // 相同优先级按创建时间排序
        return new Date(a.createdAt) - new Date(b.createdAt)
      })

    return pendingTasks[0] || null
  }

  /**
   * 执行单个任务
   */
  async executeTask(task) {
    try {
      console.log(`开始执行任务: ${task.id}`)
      
      // 更新任务状态
      this.updateTaskStatus(task.id, 'running')
      
      // 创建RPA执行器
      const executor = new RpaTaskExecutor({
        headless: true,
        slowMo: 100,
        timeout: this.options.taskTimeout,
        targetSystem: task.targetSystem
      })
      
      this.executors.set(task.id, executor)
      
      // 设置超时处理
      const timeoutId = setTimeout(() => {
        this.handleTaskTimeout(task.id)
      }, this.options.taskTimeout)
      
      // 执行RPA任务
      const result = await executor.executeTask(task.id, task.formData)
      
      clearTimeout(timeoutId)
      this.executors.delete(task.id)
      
      if (result.success) {
        this.handleTaskSuccess(task.id, result)
      } else {
        this.handleTaskFailure(task.id, result.error, result.logs)
      }
      
    } catch (error) {
      console.error(`任务执行异常: ${task.id}`, error)
      this.handleTaskFailure(task.id, error.message)
    }
    
    // 继续处理队列中的其他任务
    setTimeout(() => this.processQueue(), 1000)
  }

  /**
   * 处理任务成功
   */
  handleTaskSuccess(taskId, result) {
    const task = this.tasks.get(taskId)
    if (!task) return
    
    task.status = 'completed'
    task.result = result
    task.completedAt = new Date().toISOString()
    task.updatedAt = new Date().toISOString()
    
    this.updateStats()
    this.emit('task-completed', task)
    
    console.log(`任务执行成功: ${taskId}`)
  }

  /**
   * 处理任务失败
   */
  handleTaskFailure(taskId, error, logs = []) {
    const task = this.tasks.get(taskId)
    if (!task) return
    
    task.attempts += 1
    task.error = error
    task.logs = [...task.logs, ...logs]
    task.updatedAt = new Date().toISOString()
    
    if (task.attempts < task.maxAttempts) {
      // 重试任务
      task.status = 'pending'
      task.retryAt = new Date(Date.now() + this.options.retryDelay).toISOString()
      
      console.log(`任务将重试: ${taskId} (${task.attempts}/${task.maxAttempts})`)
      
      // 延迟重试
      setTimeout(() => {
        this.processQueue()
      }, this.options.retryDelay)
      
    } else {
      // 任务最终失败
      task.status = 'failed'
      task.failedAt = new Date().toISOString()
      
      console.log(`任务最终失败: ${taskId}`)
      this.emit('task-failed', task)
    }
    
    this.updateStats()
  }

  /**
   * 处理任务超时
   */
  handleTaskTimeout(taskId) {
    console.log(`任务执行超时: ${taskId}`)
    
    const executor = this.executors.get(taskId)
    if (executor) {
      executor.stop()
      this.executors.delete(taskId)
    }
    
    this.handleTaskFailure(taskId, '任务执行超时')
  }

  /**
   * 取消任务
   */
  cancelTask(taskId) {
    const task = this.tasks.get(taskId)
    if (!task) return false
    
    if (task.status === 'running') {
      const executor = this.executors.get(taskId)
      if (executor) {
        executor.stop()
        this.executors.delete(taskId)
      }
    }
    
    task.status = 'cancelled'
    task.cancelledAt = new Date().toISOString()
    task.updatedAt = new Date().toISOString()
    
    this.updateStats()
    this.emit('task-cancelled', task)
    
    console.log(`任务已取消: ${taskId}`)
    return true
  }

  /**
   * 重试任务
   */
  retryTask(taskId) {
    const task = this.tasks.get(taskId)
    if (!task || !['failed', 'cancelled'].includes(task.status)) {
      return false
    }
    
    task.status = 'pending'
    task.attempts = 0
    task.error = null
    task.updatedAt = new Date().toISOString()
    
    this.updateStats()
    this.emit('task-retried', task)
    
    console.log(`任务已重新排队: ${taskId}`)
    
    // 立即处理队列
    this.processQueue()
    
    return true
  }

  /**
   * 获取任务信息
   */
  getTask(taskId) {
    return this.tasks.get(taskId)
  }

  /**
   * 获取所有任务
   */
  getAllTasks() {
    return Array.from(this.tasks.values())
  }

  /**
   * 获取运行中的任务数量
   */
  getRunningTasksCount() {
    return Array.from(this.tasks.values()).filter(task => task.status === 'running').length
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    const tasks = Array.from(this.tasks.values())
    
    this.stats = {
      total: tasks.length,
      pending: tasks.filter(t => t.status === 'pending').length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length
    }
    
    this.emit('stats-updated', this.stats)
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(taskId, status) {
    const task = this.tasks.get(taskId)
    if (!task) return
    
    task.status = status
    task.updatedAt = new Date().toISOString()
    
    if (status === 'running') {
      task.startedAt = new Date().toISOString()
    }
    
    this.updateStats()
    this.emit('task-status-changed', task)
  }

  /**
   * 清理已完成的任务
   */
  clearCompletedTasks() {
    const completedTasks = Array.from(this.tasks.values())
      .filter(task => ['completed', 'failed', 'cancelled'].includes(task.status))
    
    completedTasks.forEach(task => {
      this.tasks.delete(task.id)
    })
    
    this.updateStats()
    this.emit('tasks-cleared', completedTasks.length)
    
    console.log(`已清理 ${completedTasks.length} 个已完成的任务`)
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件回调执行失败: ${event}`, error)
        }
      })
    }
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      stats: this.stats,
      runningTasks: this.getRunningTasksCount(),
      maxConcurrent: this.options.maxConcurrent
    }
  }
}

// 创建全局任务队列实例
export const globalTaskQueue = new TaskQueue({
  maxConcurrent: 3,
  retryAttempts: 3,
  retryDelay: 5000
})

export default TaskQueue
