/**
 * Playwright RPA 自动化脚本
 * 用于实际的浏览器自动化操作
 */

import { chromium, firefox, webkit } from '@playwright/test'

export class PlaywrightRpa {
  constructor(options = {}) {
    this.browser = null
    this.page = null
    this.context = null
    this.options = {
      headless: false, // 默认显示浏览器
      slowMo: 100,     // 操作间延迟
      timeout: 30000,  // 默认超时时间
      browserType: 'chromium', // 浏览器类型
      ...options
    }
    this.logs = []
  }

  /**
   * 初始化浏览器
   */
  async initialize() {
    try {
      this.addLog('info', '正在启动浏览器...')
      
      // 根据配置选择浏览器
      let browserLauncher
      switch (this.options.browserType) {
        case 'firefox':
          browserLauncher = firefox
          break
        case 'webkit':
          browserLauncher = webkit
          break
        default:
          browserLauncher = chromium
      }

      this.browser = await browserLauncher.launch({
        headless: this.options.headless,
        slowMo: this.options.slowMo
      })

      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 }
      })

      this.page = await this.context.newPage()
      
      // 设置默认超时时间
      this.page.setDefaultTimeout(this.options.timeout)
      
      this.addLog('success', '浏览器启动成功')
    } catch (error) {
      this.addLog('error', `浏览器启动失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 导航到指定URL
   */
  async navigateTo(url) {
    try {
      this.addLog('info', `导航到: ${url}`)
      await this.page.goto(url, { waitUntil: 'networkidle' })
      this.addLog('success', '页面加载完成')
    } catch (error) {
      this.addLog('error', `页面导航失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 自动填充表单
   */
  async fillForm(formData, selectors = {}) {
    try {
      this.addLog('info', '开始自动填充表单')
      
      // 默认选择器映射
      const defaultSelectors = {
        name: '[data-testid="name-input"], input[name="name"], #name',
        email: '[data-testid="email-input"], input[name="email"], #email',
        phone: '[data-testid="phone-input"], input[name="phone"], #phone',
        gender: '[data-testid="gender-select"], select[name="gender"], #gender',
        birthDate: '[data-testid="birth-date-picker"], input[name="birthDate"], #birthDate',
        idCard: '[data-testid="id-card-input"], input[name="idCard"], #idCard',
        address: '[data-testid="address-textarea"], textarea[name="address"], #address',
        company: '[data-testid="company-input"], input[name="company"], #company',
        position: '[data-testid="position-input"], input[name="position"], #position',
        experience: '[data-testid="experience-select"], select[name="experience"], #experience',
        salary: '[data-testid="salary-select"], select[name="salary"], #salary',
        hobbies: '[data-testid="hobbies-textarea"], textarea[name="hobbies"], #hobbies'
      }

      const finalSelectors = { ...defaultSelectors, ...selectors }

      // 逐个填充字段
      for (const [field, value] of Object.entries(formData)) {
        if (!value || !finalSelectors[field]) continue

        try {
          this.addLog('info', `填充字段: ${field}`)
          
          const selector = finalSelectors[field]
          await this.page.waitForSelector(selector, { timeout: 5000 })
          
          // 根据元素类型选择填充方式
          const element = await this.page.$(selector)
          const tagName = await element.evaluate(el => el.tagName.toLowerCase())
          const inputType = await element.evaluate(el => el.type)

          if (tagName === 'select') {
            await this.page.selectOption(selector, value)
          } else if (tagName === 'textarea') {
            await this.page.fill(selector, value)
          } else if (inputType === 'checkbox') {
            if (Array.isArray(value)) {
              // 处理多选框
              for (const checkValue of value) {
                const checkboxSelector = `${selector}[value="${checkValue}"]`
                await this.page.check(checkboxSelector)
              }
            }
          } else if (inputType === 'radio') {
            const radioSelector = `${selector}[value="${value}"]`
            await this.page.check(radioSelector)
          } else {
            await this.page.fill(selector, String(value))
          }

          this.addLog('success', `字段 ${field} 填充完成`)
          
          // 添加随机延迟，模拟人工操作
          await this.randomDelay(200, 800)
          
        } catch (error) {
          this.addLog('warning', `字段 ${field} 填充失败: ${error.message}`)
        }
      }

      this.addLog('success', '表单填充完成')
    } catch (error) {
      this.addLog('error', `表单填充失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 处理技能多选框
   */
  async fillSkills(skills) {
    try {
      this.addLog('info', '填充技能标签')
      
      for (const skill of skills) {
        const skillSelector = `[data-testid="skills-checkbox"] input[value="${skill}"]`
        try {
          await this.page.check(skillSelector)
          this.addLog('info', `选中技能: ${skill}`)
        } catch (error) {
          this.addLog('warning', `技能 ${skill} 选择失败`)
        }
      }
    } catch (error) {
      this.addLog('error', `技能填充失败: ${error.message}`)
    }
  }

  /**
   * 文件上传
   */
  async uploadFile(filePath, selector = '[data-testid="resume-upload"] input[type="file"]') {
    try {
      this.addLog('info', `上传文件: ${filePath}`)
      await this.page.setInputFiles(selector, filePath)
      this.addLog('success', '文件上传完成')
    } catch (error) {
      this.addLog('error', `文件上传失败: ${error.message}`)
    }
  }

  /**
   * 提交表单
   */
  async submitForm(submitSelector = '[data-testid="submit-button"]') {
    try {
      this.addLog('info', '提交表单')
      await this.page.click(submitSelector)
      
      // 等待提交结果
      await this.page.waitForTimeout(2000)
      this.addLog('success', '表单提交完成')
    } catch (error) {
      this.addLog('error', `表单提交失败: ${error.message}`)
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(path = `screenshot-${Date.now()}.png`) {
    try {
      await this.page.screenshot({ path, fullPage: true })
      this.addLog('info', `截图保存: ${path}`)
      return path
    } catch (error) {
      this.addLog('error', `截图失败: ${error.message}`)
    }
  }

  /**
   * 等待元素
   */
  async waitForElement(selector, timeout = 10000) {
    try {
      await this.page.waitForSelector(selector, { timeout })
      this.addLog('info', `元素已出现: ${selector}`)
    } catch (error) {
      this.addLog('error', `等待元素超时: ${selector}`)
      throw error
    }
  }

  /**
   * 执行JavaScript代码
   */
  async executeScript(script) {
    try {
      const result = await this.page.evaluate(script)
      this.addLog('info', '脚本执行完成')
      return result
    } catch (error) {
      this.addLog('error', `脚本执行失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 随机延迟
   */
  async randomDelay(min = 100, max = 500) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min
    await this.page.waitForTimeout(delay)
  }

  /**
   * 添加日志
   */
  addLog(type, message) {
    const log = {
      type,
      message,
      timestamp: new Date().toISOString(),
      time: new Date().toLocaleTimeString()
    }
    this.logs.push(log)
    console.log(`[${log.time}] ${type.toUpperCase()}: ${message}`)
  }

  /**
   * 获取日志
   */
  getLogs() {
    return this.logs
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
  }

  /**
   * 关闭浏览器
   */
  async close() {
    try {
      if (this.browser) {
        await this.browser.close()
        this.addLog('info', '浏览器已关闭')
      }
    } catch (error) {
      this.addLog('error', `关闭浏览器失败: ${error.message}`)
    }
  }
}

/**
 * RPA任务执行器
 */
export class RpaTaskExecutor {
  constructor(options = {}) {
    this.rpa = new PlaywrightRpa(options)
    this.isRunning = false
  }

  /**
   * 执行完整的表单填充任务
   */
  async executeFormFillTask(url, formData, options = {}) {
    try {
      this.isRunning = true
      
      await this.rpa.initialize()
      await this.rpa.navigateTo(url)
      
      // 等待表单加载
      await this.rpa.waitForElement('[data-testid="complex-form"]')
      
      // 填充基本信息
      await this.rpa.fillForm(formData, options.selectors)
      
      // 处理技能多选
      if (formData.skills && Array.isArray(formData.skills)) {
        await this.rpa.fillSkills(formData.skills)
      }
      
      // 上传文件（如果有）
      if (options.resumeFile) {
        await this.rpa.uploadFile(options.resumeFile)
      }
      
      // 截图
      if (options.takeScreenshot) {
        await this.rpa.takeScreenshot()
      }
      
      // 提交表单（如果需要）
      if (options.autoSubmit) {
        await this.rpa.submitForm()
      }
      
      return {
        success: true,
        logs: this.rpa.getLogs()
      }
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        logs: this.rpa.getLogs()
      }
    } finally {
      this.isRunning = false
      if (options.autoClose !== false) {
        await this.rpa.close()
      }
    }
  }

  /**
   * 停止任务
   */
  async stop() {
    this.isRunning = false
    await this.rpa.close()
  }
}

export default PlaywrightRpa
