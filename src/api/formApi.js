/**
 * 表单数据提交API服务
 * 处理表单数据提交和RPA任务触发
 */

// 模拟后端API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'

/**
 * HTTP请求工具类
 */
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return {
        success: true,
        data,
        status: response.status
      }
    } catch (error) {
      console.error('API request failed:', error)
      return {
        success: false,
        error: error.message,
        status: error.status || 500
      }
    }
  }

  get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    return this.request(url, { method: 'GET' })
  }

  post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' })
  }
}

// 创建API客户端实例
const apiClient = new ApiClient()

/**
 * 表单数据API服务
 */
export class FormApiService {
  /**
   * 提交表单数据到我们的系统
   */
  static async submitFormData(formData) {
    try {
      console.log('提交表单数据:', formData)
      
      // 数据验证
      const validation = this.validateFormData(formData)
      if (!validation.isValid) {
        return {
          success: false,
          error: '数据验证失败',
          details: validation.errors
        }
      }

      // 提交到后端API
      const result = await apiClient.post('/forms/submit', {
        formData,
        timestamp: new Date().toISOString(),
        source: 'vue3-frontend'
      })

      if (result.success) {
        // 提交成功后触发RPA流程
        await this.triggerRpaProcess(result.data.formId, formData)
      }

      return result
    } catch (error) {
      console.error('表单提交失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 触发RPA自动录入流程
   */
  static async triggerRpaProcess(formId, formData) {
    try {
      console.log('触发RPA流程:', { formId, formData })
      
      const rpaTask = {
        taskId: `rpa_${formId}_${Date.now()}`,
        formId,
        formData,
        targetSystem: 'external-system', // 目标系统标识
        priority: 'normal',
        createdAt: new Date().toISOString(),
        status: 'pending'
      }

      // 提交RPA任务到任务队列
      const result = await apiClient.post('/rpa/tasks', rpaTask)
      
      if (result.success) {
        console.log('RPA任务创建成功:', result.data.taskId)
        
        // 通知前端RPA任务已创建
        this.notifyRpaTaskCreated(result.data)
      }

      return result
    } catch (error) {
      console.error('RPA任务触发失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取RPA任务状态
   */
  static async getRpaTaskStatus(taskId) {
    try {
      const result = await apiClient.get(`/rpa/tasks/${taskId}`)
      return result
    } catch (error) {
      console.error('获取RPA任务状态失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 获取RPA任务列表
   */
  static async getRpaTaskList(params = {}) {
    try {
      const result = await apiClient.get('/rpa/tasks', params)
      return result
    } catch (error) {
      console.error('获取RPA任务列表失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 取消RPA任务
   */
  static async cancelRpaTask(taskId) {
    try {
      const result = await apiClient.delete(`/rpa/tasks/${taskId}`)
      return result
    } catch (error) {
      console.error('取消RPA任务失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 重试RPA任务
   */
  static async retryRpaTask(taskId) {
    try {
      const result = await apiClient.post(`/rpa/tasks/${taskId}/retry`)
      return result
    } catch (error) {
      console.error('重试RPA任务失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 批量提交表单数据
   */
  static async batchSubmitFormData(formDataList) {
    try {
      console.log('批量提交表单数据:', formDataList.length, '条记录')
      
      const results = []
      
      for (let i = 0; i < formDataList.length; i++) {
        const formData = formDataList[i]
        
        try {
          const result = await this.submitFormData(formData)
          results.push({
            index: i,
            formData,
            result
          })
          
          // 添加延迟避免请求过于频繁
          if (i < formDataList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        } catch (error) {
          results.push({
            index: i,
            formData,
            result: {
              success: false,
              error: error.message
            }
          })
        }
      }

      return {
        success: true,
        data: {
          total: formDataList.length,
          successful: results.filter(r => r.result.success).length,
          failed: results.filter(r => !r.result.success).length,
          results
        }
      }
    } catch (error) {
      console.error('批量提交失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 验证表单数据
   */
  static validateFormData(formData) {
    const errors = []
    
    // 必填字段验证
    const requiredFields = ['name', 'email', 'phone']
    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        errors.push(`${field} 是必填字段`)
      }
    })

    // 邮箱格式验证
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('邮箱格式不正确')
    }

    // 手机号格式验证
    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      errors.push('手机号格式不正确')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 通知RPA任务创建
   */
  static notifyRpaTaskCreated(taskData) {
    // 可以通过事件总线或状态管理通知其他组件
    window.dispatchEvent(new CustomEvent('rpa-task-created', {
      detail: taskData
    }))
  }
}

/**
 * 模拟后端API响应（开发环境使用）
 */
export class MockApiService {
  static async submitFormData(formData) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟成功响应
    return {
      success: true,
      data: {
        formId: `form_${Date.now()}`,
        message: '表单提交成功',
        timestamp: new Date().toISOString()
      }
    }
  }

  static async triggerRpaProcess(formId, formData) {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      success: true,
      data: {
        taskId: `rpa_${formId}_${Date.now()}`,
        status: 'pending',
        message: 'RPA任务已创建'
      }
    }
  }

  static async getRpaTaskStatus(taskId) {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const statuses = ['pending', 'running', 'completed', 'failed']
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    
    return {
      success: true,
      data: {
        taskId,
        status: randomStatus,
        progress: randomStatus === 'running' ? Math.floor(Math.random() * 100) : 100,
        message: `任务状态: ${randomStatus}`,
        updatedAt: new Date().toISOString()
      }
    }
  }
}

// 根据环境选择API服务
export const formApi = import.meta.env.MODE === 'development' ? MockApiService : FormApiService

export default FormApiService
