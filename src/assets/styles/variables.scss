// 颜色变量 - 根据图片调整
$primary-color: #4FC3F7;
$success-color: #66BB6A;
$warning-color: #FFB74D;
$danger-color: #EF5350;
$info-color: #78909C;

// 背景颜色
$bg-color: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f5e8 100%);
$white: #ffffff;
$gray-light: #f8f9fa;
$gray-medium: #e4e7ed;
$gray-dark: #606266;

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框颜色
$border-color: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;

// 阴影
$box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

// 圆角
$border-radius: 4px;
$border-radius-small: 2px;
$border-radius-large: 8px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 特定颜色 - 根据图片调整
$green-primary: #66BB6A;
$green-light: #81C784;
$blue-primary: #42A5F5;
$blue-light: #64B5F6;
$orange-primary: #FFA726;
$orange-light: #FFB74D;
$red-primary: #EF5350;
$red-light: #E57373;
$purple-primary: #AB47BC;
$purple-light: #BA68C8;

// 图表颜色 - 根据图片调整
$chart-colors: (
  primary: #66BB6A,
  success: #81C784,
  warning: #FFB74D,
  danger: #EF5350,
  info: #64B5F6,
  purple: #AB47BC,
  orange: #FFA726
);
