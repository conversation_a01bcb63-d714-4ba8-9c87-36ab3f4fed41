@use './variables.scss' as *;

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: $bg-color;
  color: $text-primary;
  line-height: 1.5;
}

// 通用布局类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 通用卡片样式
.card {
  background: $white;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-md;
  
  .icon {
    width: 20px;
    height: 20px;
    margin-right: $spacing-sm;
    color: $primary-color;
  }
  
  .title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
  }
}

// 统计卡片样式
.stat-card {
  background: $white;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $primary-color, $success-color);
  }
  
  .stat-value {
    font-size: $font-size-xxl;
    font-weight: bold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }
  
  .stat-label {
    font-size: $font-size-sm;
    color: $text-secondary;
  }
  
  .stat-unit {
    font-size: $font-size-sm;
    color: $text-secondary;
    margin-left: $spacing-xs;
  }
}

// 指标卡片样式
.metric-card {
  background: $white;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  padding: $spacing-md;
  text-align: center;
  
  .metric-value {
    font-size: $font-size-xl;
    font-weight: bold;
    margin-bottom: $spacing-xs;
  }
  
  .metric-label {
    font-size: $font-size-sm;
    color: $text-secondary;
    margin-bottom: $spacing-xs;
  }
  
  .metric-desc {
    font-size: $font-size-xs;
    color: $text-placeholder;
  }
}

// 表格样式
.info-table {
  width: 100%;
  border-collapse: collapse;
  
  td {
    padding: $spacing-sm $spacing-md;
    border-bottom: 1px solid $border-lighter;
    
    &:first-child {
      color: $text-secondary;
      width: 120px;
    }
    
    &:last-child {
      color: $text-primary;
    }
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }

.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }

.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }

// 响应式
@media (max-width: 768px) {
  .container {
    padding: 0 $spacing-sm;
  }
  
  .card {
    padding: $spacing-md;
  }
}
