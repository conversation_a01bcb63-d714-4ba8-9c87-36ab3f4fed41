/**
 * RPA任务管理组合式函数
 * 处理表单提交和RPA任务的完整流程
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { FormApiService, formApi } from '@/api/formApi'

export function useRpaTask() {
  // 响应式状态
  const isSubmitting = ref(false)
  const rpaTasks = ref([])
  const currentTask = ref(null)
  const taskStats = reactive({
    total: 0,
    pending: 0,
    running: 0,
    completed: 0,
    failed: 0
  })

  // 轮询定时器
  let pollTimer = null

  // 计算属性
  const hasActiveTasks = computed(() => 
    rpaTasks.value.some(task => ['pending', 'running'].includes(task.status))
  )

  const completionRate = computed(() => {
    if (taskStats.total === 0) return 0
    return Math.round((taskStats.completed / taskStats.total) * 100)
  })

  /**
   * 提交表单数据并触发RPA流程
   */
  const submitFormWithRpa = async (formData, options = {}) => {
    try {
      isSubmitting.value = true
      
      console.log('开始提交表单并触发RPA流程:', formData)
      
      // 1. 提交表单数据到我们的系统
      const submitResult = await formApi.submitFormData(formData)
      
      if (!submitResult.success) {
        throw new Error(submitResult.error || '表单提交失败')
      }

      ElMessage.success('表单提交成功！')
      
      // 2. 如果启用了RPA，等待RPA任务创建
      if (options.enableRpa !== false) {
        await waitForRpaTaskCreation(submitResult.data.formId)
      }

      return submitResult
      
    } catch (error) {
      console.error('提交流程失败:', error)
      ElMessage.error(`提交失败: ${error.message}`)
      throw error
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 批量提交表单数据
   */
  const batchSubmitWithRpa = async (formDataList, options = {}) => {
    try {
      isSubmitting.value = true
      
      console.log('开始批量提交:', formDataList.length, '条数据')
      
      const result = await FormApiService.batchSubmitFormData(formDataList)
      
      if (result.success) {
        const { successful, failed, total } = result.data
        
        ElNotification({
          title: '批量提交完成',
          message: `总计: ${total}, 成功: ${successful}, 失败: ${failed}`,
          type: successful === total ? 'success' : 'warning',
          duration: 5000
        })
        
        // 刷新任务列表
        await refreshTaskList()
      }

      return result
      
    } catch (error) {
      console.error('批量提交失败:', error)
      ElMessage.error(`批量提交失败: ${error.message}`)
      throw error
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 等待RPA任务创建
   */
  const waitForRpaTaskCreation = async (formId, timeout = 10000) => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkTask = async () => {
        try {
          await refreshTaskList()
          
          const task = rpaTasks.value.find(t => t.formId === formId)
          if (task) {
            currentTask.value = task
            ElNotification({
              title: 'RPA任务已创建',
              message: `任务ID: ${task.taskId}`,
              type: 'info',
              duration: 3000
            })
            resolve(task)
            return
          }
          
          // 检查超时
          if (Date.now() - startTime > timeout) {
            reject(new Error('等待RPA任务创建超时'))
            return
          }
          
          // 继续等待
          setTimeout(checkTask, 1000)
          
        } catch (error) {
          reject(error)
        }
      }
      
      checkTask()
    })
  }

  /**
   * 刷新任务列表
   */
  const refreshTaskList = async () => {
    try {
      const result = await FormApiService.getRpaTaskList()
      
      if (result.success) {
        rpaTasks.value = result.data.tasks || []
        updateTaskStats()
      }
      
      return result
    } catch (error) {
      console.error('刷新任务列表失败:', error)
    }
  }

  /**
   * 更新任务统计
   */
  const updateTaskStats = () => {
    const stats = {
      total: rpaTasks.value.length,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0
    }
    
    rpaTasks.value.forEach(task => {
      stats[task.status] = (stats[task.status] || 0) + 1
    })
    
    Object.assign(taskStats, stats)
  }

  /**
   * 获取任务状态
   */
  const getTaskStatus = async (taskId) => {
    try {
      const result = await FormApiService.getRpaTaskStatus(taskId)
      
      if (result.success) {
        // 更新本地任务状态
        const taskIndex = rpaTasks.value.findIndex(t => t.taskId === taskId)
        if (taskIndex !== -1) {
          rpaTasks.value[taskIndex] = { ...rpaTasks.value[taskIndex], ...result.data }
          updateTaskStats()
        }
      }
      
      return result
    } catch (error) {
      console.error('获取任务状态失败:', error)
    }
  }

  /**
   * 取消任务
   */
  const cancelTask = async (taskId) => {
    try {
      const result = await FormApiService.cancelRpaTask(taskId)
      
      if (result.success) {
        ElMessage.success('任务已取消')
        await refreshTaskList()
      } else {
        ElMessage.error('取消任务失败')
      }
      
      return result
    } catch (error) {
      console.error('取消任务失败:', error)
      ElMessage.error(`取消任务失败: ${error.message}`)
    }
  }

  /**
   * 重试任务
   */
  const retryTask = async (taskId) => {
    try {
      const result = await FormApiService.retryRpaTask(taskId)
      
      if (result.success) {
        ElMessage.success('任务已重新启动')
        await refreshTaskList()
      } else {
        ElMessage.error('重试任务失败')
      }
      
      return result
    } catch (error) {
      console.error('重试任务失败:', error)
      ElMessage.error(`重试任务失败: ${error.message}`)
    }
  }

  /**
   * 开始轮询任务状态
   */
  const startPolling = (interval = 5000) => {
    if (pollTimer) {
      clearInterval(pollTimer)
    }
    
    pollTimer = setInterval(async () => {
      if (hasActiveTasks.value) {
        await refreshTaskList()
      }
    }, interval)
  }

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    if (pollTimer) {
      clearInterval(pollTimer)
      pollTimer = null
    }
  }

  /**
   * 监听RPA任务创建事件
   */
  const handleRpaTaskCreated = (event) => {
    const taskData = event.detail
    console.log('收到RPA任务创建通知:', taskData)
    
    // 添加到任务列表
    const existingIndex = rpaTasks.value.findIndex(t => t.taskId === taskData.taskId)
    if (existingIndex === -1) {
      rpaTasks.value.unshift(taskData)
      updateTaskStats()
    }
  }

  /**
   * 清理已完成的任务
   */
  const clearCompletedTasks = () => {
    rpaTasks.value = rpaTasks.value.filter(task => 
      !['completed', 'failed'].includes(task.status)
    )
    updateTaskStats()
    ElMessage.success('已清理完成的任务')
  }

  /**
   * 导出任务报告
   */
  const exportTaskReport = () => {
    const report = {
      exportTime: new Date().toISOString(),
      statistics: { ...taskStats },
      tasks: rpaTasks.value.map(task => ({
        taskId: task.taskId,
        status: task.status,
        createdAt: task.createdAt,
        completedAt: task.completedAt,
        formData: task.formData
      }))
    }
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `rpa-task-report-${Date.now()}.json`
    link.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('任务报告已导出')
  }

  // 生命周期管理
  onMounted(() => {
    // 监听RPA任务创建事件
    window.addEventListener('rpa-task-created', handleRpaTaskCreated)
    
    // 初始化任务列表
    refreshTaskList()
    
    // 开始轮询
    startPolling()
  })

  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('rpa-task-created', handleRpaTaskCreated)
    
    // 停止轮询
    stopPolling()
  })

  return {
    // 状态
    isSubmitting,
    rpaTasks,
    currentTask,
    taskStats,
    
    // 计算属性
    hasActiveTasks,
    completionRate,
    
    // 方法
    submitFormWithRpa,
    batchSubmitWithRpa,
    refreshTaskList,
    getTaskStatus,
    cancelTask,
    retryTask,
    clearCompletedTasks,
    exportTaskReport,
    startPolling,
    stopPolling
  }
}

export default useRpaTask
