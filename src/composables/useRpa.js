/**
 * RPA组合式函数
 * 提供RPA任务管理的响应式接口
 */

import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { rpaEngine, RpaTaskBuilder, RpaUtils } from '@/utils/rpaEngine'

export function useRpa() {
  // 响应式状态
  const isRunning = ref(false)
  const currentTask = ref(null)
  const logs = ref([])
  const progress = reactive({
    current: 0,
    total: 0,
    percentage: 0,
    currentField: '',
    currentValue: ''
  })

  // 计算属性
  const canStart = computed(() => !isRunning.value)
  const canStop = computed(() => isRunning.value)
  const hasLogs = computed(() => logs.value.length > 0)

  /**
   * 初始化RPA引擎回调
   */
  const initializeCallbacks = () => {
    rpaEngine.setCallbacks({
      onLog: (log) => {
        logs.value.push(log)
        // 自动滚动到最新日志
        nextTick(() => {
          scrollToLatestLog()
        })
      },
      onProgress: (progressData) => {
        progress.current = progressData.current
        progress.total = progressData.total
        progress.percentage = Math.round((progressData.current / progressData.total) * 100)
        progress.currentField = progressData.field
        progress.currentValue = progressData.value
      },
      onComplete: () => {
        isRunning.value = false
        ElNotification({
          title: 'RPA任务完成',
          message: '所有任务已成功执行完成',
          type: 'success',
          duration: 3000
        })
      },
      onError: (error) => {
        isRunning.value = false
        ElMessage.error(`RPA任务执行失败: ${error.message}`)
      }
    })
  }

  /**
   * 启动表单填充任务
   */
  const startFormFillTask = async (formData, options = {}) => {
    try {
      const taskConfig = new RpaTaskBuilder()
        .setType('form-fill')
        .setFormData(formData)
        .setOptions({
          delay: options.delay || 300,
          validate: options.validate !== false,
          ...options
        })
        .build()

      isRunning.value = true
      currentTask.value = taskConfig
      
      await rpaEngine.startTask(taskConfig)
    } catch (error) {
      isRunning.value = false
      throw error
    }
  }

  /**
   * 启动批量处理任务
   */
  const startBatchProcessTask = async (dataList, options = {}) => {
    try {
      const taskConfig = new RpaTaskBuilder()
        .setType('batch-process')
        .setBatchData(dataList, options.batchSize || 10)
        .setOptions(options)
        .build()

      isRunning.value = true
      currentTask.value = taskConfig
      
      await rpaEngine.startTask(taskConfig)
    } catch (error) {
      isRunning.value = false
      throw error
    }
  }

  /**
   * 启动数据提取任务
   */
  const startDataExtractionTask = async (selectors, options = {}) => {
    try {
      const taskConfig = new RpaTaskBuilder()
        .setType('data-extraction')
        .setSelectors(selectors)
        .setOptions(options)
        .build()

      isRunning.value = true
      currentTask.value = taskConfig
      
      const result = await rpaEngine.startTask(taskConfig)
      return result
    } catch (error) {
      isRunning.value = false
      throw error
    }
  }

  /**
   * 停止当前任务
   */
  const stopTask = () => {
    rpaEngine.stopTask()
    isRunning.value = false
    currentTask.value = null
    
    ElMessage.warning('RPA任务已停止')
  }

  /**
   * 清除日志
   */
  const clearLogs = () => {
    logs.value = []
    rpaEngine.clearLogs()
  }

  /**
   * 重置进度
   */
  const resetProgress = () => {
    progress.current = 0
    progress.total = 0
    progress.percentage = 0
    progress.currentField = ''
    progress.currentValue = ''
  }

  /**
   * 滚动到最新日志
   */
  const scrollToLatestLog = () => {
    const logContainer = document.querySelector('.log-content')
    if (logContainer) {
      logContainer.scrollTop = logContainer.scrollHeight
    }
  }

  /**
   * 生成测试数据
   */
  const generateTestData = (count = 10) => {
    return RpaUtils.generateTestData(count)
  }

  /**
   * 验证表单数据
   */
  const validateFormData = (formData) => {
    return RpaUtils.validateFormData(formData)
  }

  /**
   * 导出日志
   */
  const exportLogs = (format = 'json') => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `rpa-logs-${timestamp}.${format}`
    
    let content = ''
    
    if (format === 'json') {
      content = JSON.stringify(logs.value, null, 2)
    } else if (format === 'txt') {
      content = logs.value
        .map(log => `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`)
        .join('\n')
    } else if (format === 'csv') {
      const headers = 'Time,Type,Message\n'
      const rows = logs.value
        .map(log => `"${log.time}","${log.type}","${log.message}"`)
        .join('\n')
      content = headers + rows
    }
    
    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success(`日志已导出: ${filename}`)
  }

  /**
   * 获取任务统计信息
   */
  const getTaskStats = computed(() => {
    const stats = {
      total: logs.value.length,
      success: 0,
      error: 0,
      warning: 0,
      info: 0
    }
    
    logs.value.forEach(log => {
      stats[log.type] = (stats[log.type] || 0) + 1
    })
    
    return stats
  })

  /**
   * 预设任务模板
   */
  const taskTemplates = {
    // 用户注册表单
    userRegistration: {
      name: '用户注册',
      description: '自动填充用户注册表单',
      fields: ['name', 'email', 'phone', 'password', 'confirmPassword'],
      options: { delay: 500, validate: true }
    },
    
    // 员工信息录入
    employeeInfo: {
      name: '员工信息录入',
      description: '批量录入员工基本信息',
      fields: ['name', 'gender', 'birthDate', 'idCard', 'phone', 'email', 'address', 'company', 'position'],
      options: { delay: 300, validate: true }
    },
    
    // 客户资料管理
    customerData: {
      name: '客户资料管理',
      description: '客户信息的批量处理和更新',
      fields: ['companyName', 'contactPerson', 'phone', 'email', 'address', 'industry'],
      options: { delay: 400, validate: true }
    }
  }

  /**
   * 应用任务模板
   */
  const applyTemplate = (templateName, customData = {}) => {
    const template = taskTemplates[templateName]
    if (!template) {
      throw new Error(`未找到模板: ${templateName}`)
    }
    
    const formData = {}
    template.fields.forEach(field => {
      formData[field] = customData[field] || ''
    })
    
    return {
      formData,
      options: { ...template.options, ...customData.options }
    }
  }

  // 初始化回调
  initializeCallbacks()

  return {
    // 状态
    isRunning,
    currentTask,
    logs,
    progress,
    
    // 计算属性
    canStart,
    canStop,
    hasLogs,
    getTaskStats,
    
    // 方法
    startFormFillTask,
    startBatchProcessTask,
    startDataExtractionTask,
    stopTask,
    clearLogs,
    resetProgress,
    generateTestData,
    validateFormData,
    exportLogs,
    
    // 模板相关
    taskTemplates,
    applyTemplate
  }
}

export default useRpa
