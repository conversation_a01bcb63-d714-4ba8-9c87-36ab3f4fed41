import { createRouter, createWebHistory } from "vue-router";
import Home from "@/views/Home.vue";
import CircuitBoardDemo from "@/views/CircuitBoardDemo.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: Home,
    },
    {
      path: "/circuit-board",
      name: "circuit-board",
      component: CircuitBoardDemo,
    },
  ],
});

export default router;
