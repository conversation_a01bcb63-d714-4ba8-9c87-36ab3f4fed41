import { createRouter, createWebHistory } from "vue-router";
import Home from "@/views/Home.vue";
import CircuitBoardDemo from "@/views/CircuitBoardDemo.vue";
import ComplexForm from "@/views/ComplexForm.vue";
import RpaControlPanel from "@/views/RpaControlPanel.vue";
import SystemIntegration from "@/views/SystemIntegration.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: Home,
    },
    {
      path: "/circuit-board",
      name: "circuit-board",
      component: CircuitBoardDemo,
    },
    {
      path: "/complex-form",
      name: "complex-form",
      component: ComplexForm,
    },
    {
      path: "/rpa-control",
      name: "rpa-control",
      component: RpaControlPanel,
    },
    {
      path: "/system-integration",
      name: "system-integration",
      component: SystemIntegration,
    },
  ],
});

export default router;
