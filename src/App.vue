<template>
  <div id="app">
    <el-container>
      <el-header class="app-header">
        <div class="header-content">
          <h1 class="app-title">AI Test Project</h1>
          <nav class="app-nav">
            <router-link to="/" class="nav-link">首页</router-link>
            <router-link to="/circuit-board" class="nav-link">电路板动画</router-link>
          </nav>
        </div>
      </el-header>
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
// 移除了测试代码，保持简洁
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-bottom: 2px solid #00ff41;
  padding: 0;
  height: 60px !important;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.app-title {
  color: #00ff41;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0;
}

.app-nav {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: #fff;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 65, 0.1);
    color: #00ff41;
  }

  &.router-link-active {
    background: #00ff41;
    color: #000;
  }
}

.app-main {
  padding: 0;
  background: #0a0a0a;
}
</style>
