<template>
  <el-button type="primary" @click="handleClick">提交</el-button>
</template>
<script setup>
import { ref } from "vue";
// @ts-check
import { test, expect } from '@playwright/test';
test('get started link', async ({ page }) => {
  await page.goto('https://playwright.dev/');

  // Click the get started link.
  await page.getByRole('link', { name: 'Get started' }).click();

  // Expects page to have a heading with the name of Installation.
  await expect(page.getByRole('heading', { name: 'Installation' })).toBeVisible();
});

const handleClick = () => {
  test();
};
</script>
