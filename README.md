# AI Test Project - RPA自动化表单录入系统

## 项目概述

这是一个基于Vue3 + Playwright的RPA（机器人流程自动化）表单录入解决方案，集成了复杂表单自动填充、电路板动画效果等功能。项目采用现代化的前端技术栈，提供了完整的RPA自动化工具和用户友好的管理界面。

## 🚀 核心功能

### 1. 电路板动画组件
- **路径**: `/circuit-board`
- **特性**:
  - SVG矢量图形，支持任意缩放
  - 多种动画效果：电路发光、芯片脉冲、数据流动
  - 丰富的电路元件：CPU、RAM、SSD、电阻、电容
  - 可控制的动画开关
  - 响应式设计

### 2. RPA表单自动录入
- **路径**: `/complex-form`
- **功能**:
  - 复杂表单界面（包含多种表单控件）
  - 智能自动填充功能
  - 实时进度监控
  - 详细执行日志
  - 错误处理机制
  - 批量数据处理

### 3. RPA控制面板
- **路径**: `/rpa-control`
- **特性**:
  - 专业的任务管理界面
  - 浏览器配置（Chrome、Firefox、Safari）
  - 执行模式选择（可视化/无头模式）
  - 快速模板应用
  - 日志导出功能
  - 任务统计分析

## 🛠 技术栈

### 前端框架
- **Vue 3** - 组合式API + `<script setup>`语法
- **Element Plus** - UI组件库
- **SCSS** - 样式预处理器
- **Vue Router** - 路由管理
- **Pnpm** - 包管理器

### RPA技术
- **Playwright** - 浏览器自动化引擎
- **自定义RPA引擎** - 基于Playwright的封装
- **任务调度器** - 支持批量和并发处理

### 开发工具
- **Vite** - 构建工具
- **ESLint** - 代码规范
- **Prettier** - 代码格式化

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm 8+

### 安装依赖
```bash
# 安装依赖
pnpm install

# 安装Playwright浏览器
npx playwright install
```

### 启动开发服务器
```bash
pnpm dev
```

访问 `http://localhost:5173` 查看项目

### 构建生产版本
```bash
pnpm build
```

## 📖 使用指南

### 1. 电路板动画
1. 访问首页，点击"电路板动画"卡片
2. 观察炫酷的电路板动画效果
3. 使用控制按钮启动/停止动画

### 2. RPA表单录入
1. 访问"RPA表单"页面
2. 点击右下角🤖按钮打开RPA面板
3. 点击"填充示例数据"按钮
4. 点击"启动RPA自动录入"观察自动填充过程

### 3. RPA控制面板
1. 访问"RPA控制台"页面
2. 配置任务参数（浏览器类型、执行模式等）
3. 生成或导入表单数据
4. 启动RPA任务并监控执行过程

## 🧪 测试

### 运行自动化测试
```bash
# 运行所有RPA测试
npx playwright test tests/rpa-form-automation.spec.js

# 以可视化模式运行测试
npx playwright test --headed
```

## 📚 详细文档

更多详细信息请查看：
- [RPA系统使用指南](docs/RPA_SYSTEM_GUIDE.md)
- [电路板组件文档](src/components/CircuitBoard.md)

## 📄 许可证

本项目采用 MIT 许可证
