# AI Test Project - RPA自动化表单录入系统

## 项目概述

这是一个基于Vue3 + Playwright的RPA（机器人流程自动化）表单录入解决方案，集成了复杂表单自动填充、电路板动画效果等功能。项目采用现代化的前端技术栈，提供了完整的RPA自动化工具和用户友好的管理界面。

## 🚀 核心功能

### 1. 跨系统RPA自动化 ⭐
- **路径**: `/system-integration`
- **核心流程**: Vue3系统 → API处理 → RPA任务队列 → Playwright自动化 → 目标系统
- **特性**:
  - 完整的跨系统数据流转演示
  - 实时任务状态监控
  - 可视化流程展示
  - 系统配置管理
  - 执行日志分析

### 2. RPA表单自动录入
- **路径**: `/complex-form`
- **功能**:
  - 复杂表单界面（包含多种表单控件）
  - 智能自动填充功能
  - 实时进度监控
  - 详细执行日志
  - 错误处理机制
  - 批量数据处理
  - **新增**: 表单提交后自动触发RPA流程

### 3. RPA控制面板
- **路径**: `/rpa-control`
- **特性**:
  - 专业的任务管理界面
  - 浏览器配置（Chrome、Firefox、Safari）
  - 执行模式选择（可视化/无头模式）
  - 快速模板应用
  - 日志导出功能
  - 任务统计分析

### 4. 电路板动画组件
- **路径**: `/circuit-board`
- **特性**:
  - SVG矢量图形，支持任意缩放
  - 多种动画效果：电路发光、芯片脉冲、数据流动
  - 丰富的电路元件：CPU、RAM、SSD、电阻、电容
  - 可控制的动画开关
  - 响应式设计

## 🛠 技术栈

### 前端框架
- **Vue 3** - 组合式API + `<script setup>`语法
- **Element Plus** - UI组件库
- **SCSS** - 样式预处理器
- **Vue Router** - 路由管理
- **Pnpm** - 包管理器

### RPA技术
- **Playwright** - 浏览器自动化引擎
- **CrossSystemRpa** - 跨系统自动化引擎
- **TaskQueue** - 任务队列管理系统
- **API集成** - 表单提交与RPA触发机制

### 开发工具
- **Vite** - 构建工具
- **ESLint** - 代码规范
- **Prettier** - 代码格式化

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm 8+

### 安装依赖
```bash
# 安装依赖
pnpm install

# 安装Playwright浏览器
npx playwright install
```

### 启动开发服务器
```bash
pnpm dev
```

访问 `http://localhost:5173` 查看项目

### 构建生产版本
```bash
pnpm build
```

## 📖 使用指南

### 1. 跨系统RPA集成演示 ⭐ (推荐)
1. 访问"系统集成"页面 (`/system-integration`)
2. 填写表单数据并选择目标系统
3. 点击"提交并触发RPA"按钮
4. 观察完整的跨系统自动化流程
5. 监控任务执行状态和日志

### 2. RPA表单录入
1. 访问"RPA表单"页面 (`/complex-form`)
2. 填写表单信息
3. 点击"提交表单"自动触发RPA流程
4. 查看RPA任务执行状态和日志
5. 支持批量数据处理

### 3. RPA控制面板
1. 访问"RPA控制台"页面 (`/rpa-control`)
2. 配置任务参数（浏览器类型、执行模式等）
3. 生成或导入表单数据
4. 启动RPA任务并监控执行过程

### 4. 电路板动画
1. 访问"电路板动画"页面 (`/circuit-board`)
2. 观察炫酷的电路板动画效果
3. 使用控制按钮启动/停止动画

## 🧪 测试

### 运行自动化测试
```bash
# 运行所有RPA测试
npx playwright test tests/rpa-form-automation.spec.js

# 以可视化模式运行测试
npx playwright test --headed
```

## 📚 详细文档

更多详细信息请查看：
- [跨系统RPA解决方案指南](docs/CROSS_SYSTEM_RPA_GUIDE.md) ⭐
- [RPA系统使用指南](docs/RPA_SYSTEM_GUIDE.md)
- [电路板组件文档](src/components/CircuitBoard.md)

## 🔧 核心API

### 表单提交与RPA触发
```javascript
import { useRpaTask } from '@/composables/useRpaTask'

const { submitFormWithRpa } = useRpaTask()

// 提交表单并自动触发RPA
await submitFormWithRpa(formData, {
  enableRpa: true,
  targetSystem: 'external-system'
})
```

### 跨系统RPA执行
```javascript
import { CrossSystemRpa } from '@/rpa/crossSystemRpa'

const rpa = new CrossSystemRpa({
  sourceSystem: 'http://localhost:5173',
  targetSystem: 'https://target-system.com'
})

await rpa.executeTask(taskId, formData)
```

### 任务队列管理
```javascript
import { globalTaskQueue } from '@/utils/taskQueue'

// 添加任务
const taskId = globalTaskQueue.addTask({
  formData,
  targetSystem: 'external-crm'
})

// 监听任务完成
globalTaskQueue.on('task-completed', (task) => {
  console.log('任务完成:', task.id)
})
```

## 📄 许可证

本项目采用 MIT 许可证
