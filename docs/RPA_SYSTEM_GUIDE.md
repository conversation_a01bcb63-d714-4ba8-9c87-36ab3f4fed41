# RPA自动化表单录入系统使用指南

## 系统概述

本系统是一个基于Vue3 + Playwright的RPA（机器人流程自动化）表单录入解决方案，支持复杂表单的自动填充、验证和提交，适用于批量数据录入、表单测试等场景。

## 核心功能

### 1. 复杂表单录入界面
- **路径**: `/complex-form`
- **功能**: 包含多种表单控件的复杂表单
- **支持的字段类型**:
  - 文本输入框（姓名、身份证号等）
  - 下拉选择框（性别、工作年限等）
  - 日期选择器（出生日期）
  - 多行文本框（地址、兴趣爱好）
  - 多选框（技能标签）
  - 文件上传（简历文件）

### 2. RPA自动录入功能
- **智能填充**: 自动识别表单字段并填充数据
- **进度监控**: 实时显示填充进度和当前操作
- **错误处理**: 自动处理填充失败的字段
- **日志记录**: 详细记录每个操作步骤

### 3. RPA控制面板
- **路径**: `/rpa-control`
- **功能**: 专业的RPA任务管理平台
- **特性**:
  - 任务配置管理
  - 批量数据处理
  - 执行监控和日志分析
  - 快速模板应用

## 技术架构

### 前端技术栈
- **Vue 3**: 组合式API + `<script setup>`语法
- **Element Plus**: UI组件库
- **SCSS**: 样式预处理器
- **Pinia**: 状态管理
- **Vue Router**: 路由管理

### RPA技术栈
- **Playwright**: 浏览器自动化引擎
- **自定义RPA引擎**: 基于Playwright的封装
- **任务调度器**: 支持批量和并发处理

### 项目结构
```
src/
├── components/          # 通用组件
│   ├── CircuitBoard.vue # 电路板动画组件
│   └── CustomTable.vue  # 自定义表格组件
├── views/              # 页面组件
│   ├── Home.vue        # 首页
│   ├── ComplexForm.vue # 复杂表单页面
│   └── RpaControlPanel.vue # RPA控制面板
├── composables/        # 组合式函数
│   └── useRpa.js       # RPA功能封装
├── utils/              # 工具函数
│   └── rpaEngine.js    # RPA引擎核心
├── rpa/               # RPA脚本
│   └── playwrightRpa.js # Playwright自动化脚本
└── assets/            # 静态资源
    └── styles/        # 样式文件
```

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 安装Playwright浏览器
npx playwright install
```

### 2. 基本使用

#### 访问表单页面
1. 打开浏览器访问 `http://localhost:5173/complex-form`
2. 查看复杂表单界面
3. 点击右下角的🤖按钮打开RPA面板

#### 使用RPA自动录入
1. 点击"填充示例数据"按钮
2. 点击"启动RPA自动录入"
3. 观察自动填充过程和执行日志
4. 查看填充结果

#### 使用RPA控制面板
1. 访问 `http://localhost:5173/rpa-control`
2. 配置任务参数（浏览器类型、执行模式等）
3. 生成或导入表单数据
4. 启动RPA任务并监控执行过程

### 3. 高级功能

#### 批量处理
```javascript
// 使用RPA引擎进行批量处理
import { useRpa } from '@/composables/useRpa'

const { startBatchProcessTask } = useRpa()

const batchData = [
  { name: '张三', email: '<EMAIL>' },
  { name: '李四', email: '<EMAIL>' },
  // ... 更多数据
]

await startBatchProcessTask(batchData, {
  batchSize: 10,
  delay: 500
})
```

#### 自定义RPA脚本
```javascript
// 创建自定义RPA任务
import { RpaTaskBuilder } from '@/utils/rpaEngine'

const task = new RpaTaskBuilder()
  .setType('form-fill')
  .setFormData(formData)
  .setOptions({
    delay: 300,
    validate: true,
    autoSubmit: false
  })
  .build()
```

## API参考

### useRpa组合式函数

#### 状态
- `isRunning`: 是否正在执行RPA任务
- `logs`: 执行日志数组
- `progress`: 执行进度信息

#### 方法
- `startFormFillTask(formData, options)`: 启动表单填充任务
- `startBatchProcessTask(dataList, options)`: 启动批量处理任务
- `stopTask()`: 停止当前任务
- `clearLogs()`: 清除执行日志
- `exportLogs(format)`: 导出日志文件

### RPA引擎配置

#### 基本配置
```javascript
const options = {
  delay: 300,        // 操作间延迟（毫秒）
  validate: true,    // 是否进行数据验证
  autoSubmit: false, // 是否自动提交表单
  takeScreenshot: true, // 是否执行截图
  batchSize: 10      // 批量处理大小
}
```

#### 浏览器配置
```javascript
const browserOptions = {
  headless: false,     // 是否无头模式
  slowMo: 100,        // 操作减速（毫秒）
  timeout: 30000,     // 超时时间
  browserType: 'chromium' // 浏览器类型
}
```

## 测试

### 运行自动化测试
```bash
# 运行所有RPA测试
npx playwright test tests/rpa-form-automation.spec.js

# 运行特定测试
npx playwright test tests/rpa-form-automation.spec.js --grep "基本表单填充"

# 以可视化模式运行测试
npx playwright test --headed
```

### 测试覆盖范围
- ✅ 基本表单填充功能
- ✅ RPA自动录入流程
- ✅ 表单验证机制
- ✅ 批量处理功能
- ✅ 响应式设计
- ✅ 控制面板功能
- ✅ 完整集成流程

## 部署指南

### 生产环境部署
```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 5173
CMD ["npm", "run", "preview"]
```

## 故障排除

### 常见问题

#### 1. RPA任务执行失败
- **原因**: 页面元素未找到或超时
- **解决**: 检查选择器是否正确，增加等待时间

#### 2. 浏览器启动失败
- **原因**: Playwright浏览器未安装
- **解决**: 运行 `npx playwright install`

#### 3. 表单验证失败
- **原因**: 数据格式不符合验证规则
- **解决**: 检查数据格式，确保符合表单要求

### 调试技巧

#### 1. 启用详细日志
```javascript
// 在RPA配置中启用调试模式
const options = {
  debug: true,
  verbose: true
}
```

#### 2. 使用可视化模式
```javascript
// 设置浏览器为可视化模式
const browserOptions = {
  headless: false,
  slowMo: 1000 // 减慢操作速度便于观察
}
```

#### 3. 截图调试
```javascript
// 在关键步骤执行截图
await page.screenshot({ path: 'debug-screenshot.png' })
```

## 扩展开发

### 添加新的表单字段
1. 在`ComplexForm.vue`中添加表单控件
2. 更新`formData`响应式对象
3. 在RPA脚本中添加对应的选择器
4. 更新验证规则

### 集成其他RPA工具
1. 创建新的RPA引擎适配器
2. 实现统一的接口规范
3. 更新配置选项
4. 添加相应的测试用例

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 编写测试用例
5. 提交Pull Request

## 许可证

MIT License - 详见LICENSE文件

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 文档更新: [Wiki]
