# 跨系统RPA自动化解决方案

## 系统架构概述

本项目实现了一个完整的跨系统RPA（机器人流程自动化）解决方案，支持从Vue3系统提交数据到外部系统的自动化录入流程。

### 核心流程

```
Vue3系统 → API处理 → RPA任务队列 → Playwright自动化 → 目标系统
    ↓         ↓           ↓              ↓            ↓
  表单提交   数据验证    任务调度       浏览器操作     数据录入
```

## 系统组件

### 1. 前端Vue3系统

#### 主要页面
- **首页** (`/`) - 功能导航和概览
- **复杂表单** (`/complex-form`) - 数据录入界面
- **RPA控制台** (`/rpa-control`) - 任务管理界面
- **系统集成演示** (`/system-integration`) - 完整流程演示
- **电路板动画** (`/circuit-board`) - 视觉效果展示

#### 核心组件
- `ComplexForm.vue` - 复杂表单录入组件
- `RpaControlPanel.vue` - RPA任务管理面板
- `SystemIntegration.vue` - 系统集成演示
- `CircuitBoard.vue` - 电路板动画组件

### 2. API服务层

#### FormApiService (`src/api/formApi.js`)
```javascript
// 提交表单数据并触发RPA
await FormApiService.submitFormData(formData)

// 批量提交数据
await FormApiService.batchSubmitFormData(dataList)

// 获取RPA任务状态
await FormApiService.getRpaTaskStatus(taskId)
```

#### 主要功能
- 表单数据验证和提交
- RPA任务创建和管理
- 批量数据处理
- 任务状态跟踪

### 3. RPA引擎

#### CrossSystemRpa (`src/rpa/crossSystemRpa.js`)
基于Playwright的跨系统自动化引擎：

```javascript
const rpa = new CrossSystemRpa({
  headless: false,
  slowMo: 200,
  sourceSystem: 'http://localhost:5173',
  targetSystem: 'https://target-system.com'
})

// 执行跨系统任务
const result = await rpa.executeTask(taskId, formData)
```

#### 执行流程
1. **数据获取** - 从源系统API获取任务数据
2. **系统登录** - 自动登录目标系统
3. **页面导航** - 导航到目标表单页面
4. **表单填充** - 智能识别并填充表单字段
5. **数据提交** - 提交表单并验证结果
6. **结果反馈** - 返回执行结果和日志

### 4. 任务队列系统

#### TaskQueue (`src/utils/taskQueue.js`)
```javascript
// 添加任务到队列
const taskId = taskQueue.addTask({
  formId: 'form_123',
  formData: userData,
  targetSystem: 'external-crm'
})

// 监听任务事件
taskQueue.on('task-completed', (task) => {
  console.log('任务完成:', task.id)
})
```

#### 特性
- **并发控制** - 支持最大并发数限制
- **自动重试** - 失败任务自动重试机制
- **优先级调度** - 支持任务优先级排序
- **状态管理** - 实时任务状态跟踪
- **事件通知** - 任务状态变化事件

### 5. 组合式函数

#### useRpaTask (`src/composables/useRpaTask.js`)
```javascript
const {
  isSubmitting,
  rpaTasks,
  taskStats,
  submitFormWithRpa,
  batchSubmitWithRpa
} = useRpaTask()

// 提交表单并触发RPA
await submitFormWithRpa(formData, {
  enableRpa: true,
  targetSystem: 'external-system'
})
```

## 使用指南

### 1. 基本使用流程

#### 步骤1: 表单数据录入
```vue
<template>
  <ComplexForm @submit="handleSubmit" />
</template>

<script setup>
import { useRpaTask } from '@/composables/useRpaTask'

const { submitFormWithRpa } = useRpaTask()

const handleSubmit = async (formData) => {
  await submitFormWithRpa(formData, {
    enableRpa: true,
    targetSystem: 'external-crm'
  })
}
</script>
```

#### 步骤2: RPA任务监控
```vue
<template>
  <div>
    <div v-for="task in rpaTasks" :key="task.id">
      任务: {{ task.id }} - 状态: {{ task.status }}
    </div>
  </div>
</template>

<script setup>
const { rpaTasks, taskStats } = useRpaTask()
</script>
```

### 2. 批量处理

```javascript
// 准备批量数据
const batchData = [
  { name: '张三', email: '<EMAIL>' },
  { name: '李四', email: '<EMAIL>' },
  // ... 更多数据
]

// 批量提交
await batchSubmitWithRpa(batchData, {
  enableRpa: true,
  targetSystem: 'external-system',
  batchSize: 10
})
```

### 3. 自定义RPA脚本

```javascript
import { CrossSystemRpa } from '@/rpa/crossSystemRpa'

const customRpa = new CrossSystemRpa({
  targetSystem: 'https://your-target-system.com',
  username: 'your-username',
  password: 'your-password'
})

// 自定义字段映射
const fieldMapping = {
  name: '#target_name_field',
  email: '#target_email_field',
  phone: '#target_phone_field'
}

await customRpa.fillTargetSystemForm(sourceData, fieldMapping)
```

## 配置说明

### 环境变量
```bash
# .env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_TARGET_SYSTEM_URL=https://target-system.com
VITE_RPA_MAX_CONCURRENT=3
VITE_RPA_RETRY_ATTEMPTS=3
```

### RPA配置
```javascript
const rpaConfig = {
  // 浏览器设置
  headless: false,          // 是否无头模式
  slowMo: 200,             // 操作延迟
  timeout: 30000,          // 超时时间
  
  // 系统设置
  sourceSystem: 'http://localhost:5173',
  targetSystem: 'https://target-system.com',
  
  // 任务队列设置
  maxConcurrent: 3,        // 最大并发数
  retryAttempts: 3,        // 重试次数
  retryDelay: 5000         // 重试延迟
}
```

## 部署指南

### 1. 开发环境
```bash
# 安装依赖
pnpm install

# 安装Playwright浏览器
npx playwright install

# 启动开发服务器
pnpm dev
```

### 2. 生产环境
```bash
# 构建项目
pnpm build

# 部署到服务器
# 确保服务器安装了Playwright浏览器
```

### 3. Docker部署
```dockerfile
FROM node:18-alpine

# 安装Playwright依赖
RUN apk add --no-cache \
    chromium \
    firefox \
    webkit

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

EXPOSE 5173
CMD ["npm", "run", "preview"]
```

## 监控和日志

### 1. 任务监控
- 实时任务状态显示
- 执行进度跟踪
- 成功率统计
- 错误日志记录

### 2. 日志管理
```javascript
// 导出执行日志
const { exportLogs } = useRpa()
exportLogs('json') // 支持 json, txt, csv 格式
```

### 3. 性能监控
- 任务执行时间统计
- 系统资源使用情况
- 并发任务数量监控

## 故障排除

### 常见问题

#### 1. RPA任务执行失败
**原因**: 目标系统页面结构变化
**解决**: 更新字段选择器映射

#### 2. 浏览器启动失败
**原因**: Playwright浏览器未安装
**解决**: 运行 `npx playwright install`

#### 3. 任务队列阻塞
**原因**: 并发数设置过高或任务超时
**解决**: 调整并发数和超时时间

### 调试技巧

#### 1. 启用可视化模式
```javascript
const rpa = new CrossSystemRpa({
  headless: false,
  slowMo: 1000
})
```

#### 2. 详细日志记录
```javascript
// 启用详细日志
const options = {
  debug: true,
  verbose: true
}
```

#### 3. 截图调试
```javascript
// 在关键步骤截图
await page.screenshot({ 
  path: 'debug-screenshot.png',
  fullPage: true 
})
```

## 扩展开发

### 1. 添加新的目标系统
1. 创建新的RPA脚本类
2. 实现系统特定的登录和表单填充逻辑
3. 更新字段映射配置
4. 添加相应的测试用例

### 2. 自定义表单字段
1. 在ComplexForm.vue中添加新字段
2. 更新formData响应式对象
3. 在RPA脚本中添加字段处理逻辑
4. 更新验证规则

### 3. 集成其他RPA工具
1. 创建新的RPA引擎适配器
2. 实现统一的接口规范
3. 更新任务队列系统
4. 添加配置选项

## 最佳实践

### 1. 数据安全
- 敏感数据加密传输
- 登录凭据安全存储
- 访问权限控制

### 2. 性能优化
- 合理设置并发数
- 优化选择器查找
- 减少不必要的等待时间

### 3. 错误处理
- 完善的重试机制
- 详细的错误日志
- 用户友好的错误提示

### 4. 维护性
- 模块化代码结构
- 完整的文档说明
- 充分的测试覆盖

## 许可证

MIT License - 详见LICENSE文件
