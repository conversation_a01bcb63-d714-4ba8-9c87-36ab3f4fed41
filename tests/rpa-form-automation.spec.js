/**
 * RPA表单自动化测试脚本
 * 使用Playwright进行实际的表单自动录入测试
 */

import { test, expect } from '@playwright/test'

// 测试数据
const testFormData = {
  name: '张三',
  gender: 'male',
  birthDate: '1990-01-01',
  idCard: '110101199001011234',
  phone: '13800138000',
  email: 'z<PERSON><PERSON>@example.com',
  address: '北京市朝阳区某某街道某某小区',
  company: '某某科技有限公司',
  position: '前端开发工程师',
  experience: '3-5',
  salary: '10k-20k',
  skills: ['javascript', 'vue', 'nodejs'],
  hobbies: '喜欢编程、阅读技术书籍、打篮球'
}

test.describe('RPA表单自动录入测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到表单页面
    await page.goto('/complex-form')
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="complex-form"]')
  })

  test('基本表单填充测试', async ({ page }) => {
    console.log('开始执行基本表单填充测试')

    // 填充基本信息
    await page.fill('[data-testid="name-input"]', testFormData.name)
    await page.selectOption('[data-testid="gender-select"]', testFormData.gender)
    await page.fill('[data-testid="birth-date-picker"]', testFormData.birthDate)
    await page.fill('[data-testid="id-card-input"]', testFormData.idCard)

    // 填充联系方式
    await page.fill('[data-testid="phone-input"]', testFormData.phone)
    await page.fill('[data-testid="email-input"]', testFormData.email)
    await page.fill('[data-testid="address-textarea"]', testFormData.address)

    // 填充工作信息
    await page.fill('[data-testid="company-input"]', testFormData.company)
    await page.fill('[data-testid="position-input"]', testFormData.position)
    await page.selectOption('[data-testid="experience-select"]', testFormData.experience)
    await page.selectOption('[data-testid="salary-select"]', testFormData.salary)

    // 选择技能
    for (const skill of testFormData.skills) {
      await page.check(`[data-testid="skills-checkbox"] input[value="${skill}"]`)
    }

    // 填充兴趣爱好
    await page.fill('[data-testid="hobbies-textarea"]', testFormData.hobbies)

    // 验证填充结果
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue(testFormData.name)
    await expect(page.locator('[data-testid="email-input"]')).toHaveValue(testFormData.email)
    await expect(page.locator('[data-testid="phone-input"]')).toHaveValue(testFormData.phone)

    console.log('基本表单填充测试完成')
  })

  test('RPA自动录入功能测试', async ({ page }) => {
    console.log('开始执行RPA自动录入功能测试')

    // 点击浮动RPA按钮
    await page.click('[data-testid="floating-rpa-button"]')
    
    // 等待RPA面板出现
    await page.waitForSelector('.rpa-panel')

    // 填充示例数据
    await page.click('[data-testid="sample-data-button"]')
    
    // 等待数据填充完成
    await page.waitForTimeout(1000)

    // 启动RPA任务
    await page.click('[data-testid="start-rpa-button"]')

    // 等待RPA任务执行
    await page.waitForSelector('[data-testid="toggle-logs-button"]')
    
    // 显示执行日志
    await page.click('[data-testid="toggle-logs-button"]')

    // 验证日志内容
    await expect(page.locator('.rpa-logs')).toBeVisible()
    
    // 等待任务完成
    await page.waitForFunction(() => {
      const button = document.querySelector('[data-testid="start-rpa-button"]')
      return button && !button.disabled
    }, { timeout: 30000 })

    console.log('RPA自动录入功能测试完成')
  })

  test('表单验证测试', async ({ page }) => {
    console.log('开始执行表单验证测试')

    // 尝试提交空表单
    await page.click('[data-testid="submit-button"]')

    // 验证错误提示
    await expect(page.locator('.el-form-item__error')).toBeVisible()

    // 填充必要字段
    await page.fill('[data-testid="name-input"]', testFormData.name)
    await page.selectOption('[data-testid="gender-select"]', testFormData.gender)
    await page.fill('[data-testid="phone-input"]', testFormData.phone)
    await page.fill('[data-testid="email-input"]', testFormData.email)

    // 再次提交
    await page.click('[data-testid="submit-button"]')

    // 验证提交成功
    await expect(page.locator('.el-message--success')).toBeVisible()

    console.log('表单验证测试完成')
  })

  test('批量处理功能测试', async ({ page }) => {
    console.log('开始执行批量处理功能测试')

    // 打开RPA面板
    await page.click('[data-testid="floating-rpa-button"]')
    
    // 点击批量处理按钮
    await page.click('[data-testid="batch-process-button"]')

    // 确认批量处理对话框
    await page.click('.el-message-box__btns .el-button--primary')

    // 验证批量处理开始
    await expect(page.locator('.rpa-logs')).toBeVisible()

    console.log('批量处理功能测试完成')
  })

  test('表单重置功能测试', async ({ page }) => {
    console.log('开始执行表单重置功能测试')

    // 填充一些数据
    await page.fill('[data-testid="name-input"]', testFormData.name)
    await page.fill('[data-testid="email-input"]', testFormData.email)

    // 验证数据已填充
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue(testFormData.name)

    // 重置表单
    await page.click('[data-testid="reset-button"]')

    // 验证表单已重置
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue('')
    await expect(page.locator('[data-testid="email-input"]')).toHaveValue('')

    console.log('表单重置功能测试完成')
  })

  test('响应式设计测试', async ({ page }) => {
    console.log('开始执行响应式设计测试')

    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.complex-form')).toBeVisible()

    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page.locator('.complex-form')).toBeVisible()

    // 测试手机视图
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.complex-form')).toBeVisible()
    await expect(page.locator('.floating-rpa-button')).toBeVisible()

    console.log('响应式设计测试完成')
  })
})

test.describe('RPA控制面板测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到RPA控制面板
    await page.goto('/rpa-control')
    
    // 等待页面加载完成
    await page.waitForSelector('.rpa-control-panel')
  })

  test('控制面板基本功能测试', async ({ page }) => {
    console.log('开始执行控制面板基本功能测试')

    // 验证页面元素
    await expect(page.locator('.task-config-card')).toBeVisible()
    await expect(page.locator('.control-card')).toBeVisible()
    await expect(page.locator('.logs-card')).toBeVisible()

    // 生成示例数据
    await page.click('text=生成示例数据')
    
    // 验证数据已填充
    await expect(page.locator('input[placeholder*="姓名"]')).not.toHaveValue('')

    // 测试连接
    await page.click('text=测试连接')
    
    // 验证连接测试结果
    await expect(page.locator('.el-message--success')).toBeVisible()

    console.log('控制面板基本功能测试完成')
  })

  test('任务配置测试', async ({ page }) => {
    console.log('开始执行任务配置测试')

    // 修改任务配置
    await page.fill('input[placeholder*="任务名称"]', 'RPA自动化测试任务')
    await page.selectOption('select', 'firefox')
    await page.check('text=无头模式')

    // 验证配置已更新
    await expect(page.locator('input[placeholder*="任务名称"]')).toHaveValue('RPA自动化测试任务')

    console.log('任务配置测试完成')
  })

  test('快速模板功能测试', async ({ page }) => {
    console.log('开始执行快速模板功能测试')

    // 应用前端工程师模板
    await page.click('text=前端工程师')
    
    // 验证模板数据已应用
    await expect(page.locator('.el-message--success')).toBeVisible()

    // 应用后端工程师模板
    await page.click('text=后端工程师')
    
    // 验证模板切换成功
    await expect(page.locator('.el-message--success')).toBeVisible()

    console.log('快速模板功能测试完成')
  })
})

test.describe('集成测试', () => {
  test('完整RPA流程测试', async ({ page }) => {
    console.log('开始执行完整RPA流程测试')

    // 1. 访问控制面板
    await page.goto('/rpa-control')
    
    // 2. 配置任务
    await page.fill('input[placeholder*="任务名称"]', '完整流程测试')
    await page.click('text=生成示例数据')
    
    // 3. 启动RPA任务（模拟）
    await page.click('text=启动RPA任务')
    
    // 4. 等待任务完成
    await page.waitForSelector('.log-item', { timeout: 10000 })
    
    // 5. 验证执行日志
    await expect(page.locator('.log-item')).toHaveCount.greaterThan(0)
    
    // 6. 导航到表单页面验证
    await page.goto('/complex-form')
    await expect(page.locator('[data-testid="complex-form"]')).toBeVisible()

    console.log('完整RPA流程测试完成')
  })
})
